/**
 * Sandpack Code Editor Panel
 * 
 * Enhanced code editor panel with Sandpack integration and live preview
 */

"use client";

import * as React from "react";
import { useState, useCallback, useEffect } from "react";
import { cn } from "@/lib/utils";
import {
  SandpackProvider,
  SandpackLayout,
  SandpackCodeEditor,
  SandpackPreview,
  SandpackConsole,
  SandpackFileExplorer,
  SandpackTests,
  useSandpack,
  useActiveCode
} from '@codesandbox/sandpack-react';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Play,
  Square,
  RotateCcw,
  Eye,
  Code,
  Terminal,
  TestTube,
  FileText,
  Settings,
  <PERSON>,
  Maximize2,
  Minimize2,
  RefreshCw,
  ExternalLink
} from "lucide-react";
import { useSandpackIntegration } from "@/lib/stores/sandpack-store";
import { getAvailableTemplates } from "@/lib/sandpack-runtime/templates";

interface SandpackCodeEditorPanelProps {
  className?: string;
  projectId?: string;
  defaultTemplate?: string;
  showPreview?: boolean;
  showConsole?: boolean;
  showTests?: boolean;
}

export function SandpackCodeEditorPanel({
  className,
  projectId,
  defaultTemplate = "react",
  showPreview = true,
  showConsole = true,
  showTests = false
}: SandpackCodeEditorPanelProps) {
  const {
    instances,
    activeInstance,
    isLoading,
    error,
    createInstance,
    setActiveInstance,
    updateFile
  } = useSandpackIntegration(projectId);

  const [activeTab, setActiveTab] = useState("editor");
  const [isPreviewMaximized, setIsPreviewMaximized] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(defaultTemplate);

  const templates = getAvailableTemplates();

  // Create default instance if none exists
  useEffect(() => {
    if (!isLoading && instances.length === 0) {
      const template = templates.find(t => t.id === selectedTemplate);
      if (template) {
        createInstance({
          id: '',
          name: `${template.name} Project`,
          template: template.template,
          files: template.files,
          dependencies: template.dependencies,
          devDependencies: template.devDependencies,
          projectId,
          autorun: true,
          showConsole,
          showFileExplorer: true
        });
      }
    }
  }, [isLoading, instances.length, selectedTemplate, templates, createInstance, projectId, showConsole]);

  if (error) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-red-600">Sandpack Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{error.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading || !activeInstance) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing Sandpack...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <SandpackProvider
        template={activeInstance.config.template}
        files={activeInstance.files}
        customSetup={{
          dependencies: activeInstance.config.dependencies || {},
          devDependencies: activeInstance.config.devDependencies || {}
        }}
        options={{
          autorun: activeInstance.config.autorun,
          autoReload: activeInstance.config.autoReload
        }}
      >
        <SandpackEditorContent
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          isPreviewMaximized={isPreviewMaximized}
          setIsPreviewMaximized={setIsPreviewMaximized}
          showPreview={showPreview}
          showConsole={showConsole}
          showTests={showTests}
          activeInstance={activeInstance}
          onFileUpdate={updateFile}
        />
      </SandpackProvider>
    </div>
  );
}

// Sandpack Editor Content Component (must be inside SandpackProvider)
function SandpackEditorContent({
  activeTab,
  setActiveTab,
  isPreviewMaximized,
  setIsPreviewMaximized,
  showPreview,
  showConsole,
  showTests,
  activeInstance,
  onFileUpdate
}: {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  isPreviewMaximized: boolean;
  setIsPreviewMaximized: (maximized: boolean) => void;
  showPreview: boolean;
  showConsole: boolean;
  showTests: boolean;
  activeInstance: any;
  onFileUpdate: (instanceId: string, path: string, content: string) => Promise<void>;
}) {
  const { sandpack } = useSandpack();
  const { code, updateCode } = useActiveCode();

  const handleCodeChange = useCallback(async (newCode: string) => {
    updateCode(newCode);
    if (sandpack.activeFile && activeInstance) {
      try {
        await onFileUpdate(activeInstance.id, sandpack.activeFile, newCode);
      } catch (error) {
        console.error('Failed to update file:', error);
      }
    }
  }, [updateCode, sandpack.activeFile, activeInstance, onFileUpdate]);

  const tabs = [
    { id: "editor", label: "Editor", icon: <Code className="h-4 w-4" /> },
    ...(showPreview ? [{ id: "preview", label: "Preview", icon: <Eye className="h-4 w-4" /> }] : []),
    ...(showConsole ? [{ id: "console", label: "Console", icon: <Terminal className="h-4 w-4" /> }] : []),
    ...(showTests ? [{ id: "tests", label: "Tests", icon: <TestTube className="h-4 w-4" /> }] : [])
  ];

  return (
    <>
      {/* Header */}
      <div className="border-b border-border p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline">{activeInstance.config.template}</Badge>
            <span className="text-sm font-medium">{activeInstance.name}</span>
          </div>
          <div className="flex items-center gap-1">
            <Button size="sm" variant="ghost">
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="ghost">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1">
        {isPreviewMaximized ? (
          // Maximized Preview Mode
          <div className="h-full flex flex-col">
            <div className="border-b border-border p-2 flex items-center justify-between">
              <h3 className="font-medium">Live Preview</h3>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setIsPreviewMaximized(false)}
              >
                <Minimize2 className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex-1">
              <SandpackPreview
                showNavigator
                showRefreshButton
                showOpenInCodeSandbox
                actionsChildren={
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setIsPreviewMaximized(false)}
                  >
                    <Minimize2 className="h-4 w-4" />
                  </Button>
                }
              />
            </div>
          </div>
        ) : (
          // Normal Layout
          <ResizablePanelGroup direction="horizontal">
            {/* Editor Panel */}
            <ResizablePanel defaultSize={showPreview ? 60 : 100}>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
                <TabsList className="grid grid-cols-4 w-full">
                  {tabs.map((tab) => (
                    <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                      {tab.icon}
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>

                <TabsContent value="editor" className="flex-1 m-0">
                  <SandpackLayout>
                    <SandpackFileExplorer />
                    <SandpackCodeEditor
                      showTabs
                      showLineNumbers
                      showInlineErrors
                      wrapContent
                      closableTabs
                    />
                  </SandpackLayout>
                </TabsContent>

                {showPreview && (
                  <TabsContent value="preview" className="flex-1 m-0">
                    <SandpackPreview
                      showNavigator
                      showRefreshButton
                      showOpenInCodeSandbox
                      actionsChildren={
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setIsPreviewMaximized(true)}
                        >
                          <Maximize2 className="h-4 w-4" />
                        </Button>
                      }
                    />
                  </TabsContent>
                )}

                {showConsole && (
                  <TabsContent value="console" className="flex-1 m-0">
                    <SandpackConsole
                      showHeader
                      showSyntaxError
                      showResetConsoleButton
                    />
                  </TabsContent>
                )}

                {showTests && (
                  <TabsContent value="tests" className="flex-1 m-0">
                    <SandpackTests />
                  </TabsContent>
                )}
              </Tabs>
            </ResizablePanel>

            {/* Preview Panel (Side by Side) */}
            {showPreview && activeTab === "editor" && (
              <>
                <ResizableHandle withHandle />
                <ResizablePanel defaultSize={40}>
                  <div className="h-full flex flex-col">
                    <div className="border-b border-border p-2 flex items-center justify-between">
                      <h3 className="font-medium flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        Live Preview
                      </h3>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setIsPreviewMaximized(true)}
                      >
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex-1">
                      <SandpackPreview
                        showNavigator
                        showRefreshButton
                        showOpenInCodeSandbox
                      />
                    </div>
                  </div>
                </ResizablePanel>
              </>
            )}
          </ResizablePanelGroup>
        )}
      </div>
    </>
  );
}
