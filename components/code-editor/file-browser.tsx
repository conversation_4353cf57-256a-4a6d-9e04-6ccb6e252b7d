"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { FileNode } from "@/components/file-explorer/file-tree"
import { useFileStore } from "@/lib/stores/file-store"
import { useSandpackIntegration } from "@/lib/stores/sandpack-store"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import {
  FolderTree,
  File,
  FolderPlus,
  FilePlus,
  RefreshCw,
  Search,
  Cpu,
  Code2
} from "lucide-react"
import { Input } from "@/components/ui/input"

interface FileBrowserProps {
  onFileSelect: (file: FileNode) => void
  className?: string
}

// Helper function to get file icon based on extension
const getFileIcon = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  switch (extension) {
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
      return <Code2 className="h-4 w-4 text-blue-500" />
    case 'json':
      return <File className="h-4 w-4 text-yellow-500" />
    case 'md':
    case 'txt':
      return <File className="h-4 w-4 text-gray-500" />
    case 'html':
      return <Code2 className="h-4 w-4 text-orange-500" />
    case 'css':
    case 'scss':
    case 'sass':
      return <Code2 className="h-4 w-4 text-purple-500" />
    default:
      return <File className="h-4 w-4 text-gray-400" />
  }
}

// Enhanced File Tree Node Component with Sandpack support
const EnhancedFileTreeNode: React.FC<{
  node: FileNode
  level: number
  onFileSelect: (file: FileNode) => void
  selectedFilePath?: string
  expandedByDefault?: boolean
}> = ({ node, level, onFileSelect, selectedFilePath, expandedByDefault = false }) => {
  const [expanded, setExpanded] = React.useState(expandedByDefault)
  const isSelected = selectedFilePath === node.path
  const isSandpackFile = node.path.startsWith('/sandpack/')
  const isSandpackRoot = node.path === '/sandpack'

  // Expand parent directories of selected file
  useEffect(() => {
    if (
      node.type === 'directory' &&
      selectedFilePath &&
      selectedFilePath.startsWith(node.path + '/')
    ) {
      setExpanded(true)
    }
  }, [selectedFilePath, node.path, node.type])

  const handleClick = () => {
    if (node.type === 'directory') {
      setExpanded(!expanded)
    } else {
      onFileSelect(node)
    }
  }

  return (
    <div>
      <div
        className={cn(
          "flex items-center py-1 px-2 rounded-md cursor-pointer hover:bg-accent/50 transition-colors",
          isSelected && "bg-accent text-accent-foreground",
          isSandpackFile && "bg-blue-50 dark:bg-blue-950/20"
        )}
        style={{ paddingLeft: `${(level * 12) + 4}px` }}
        onClick={handleClick}
      >
        {node.type === 'directory' ? (
          <>
            <span className="mr-1">
              {expanded ? <Search className="h-3.5 w-3.5" /> : <FolderTree className="h-3.5 w-3.5" />}
            </span>
            {isSandpackRoot ? (
              <Cpu className="h-4 w-4 text-blue-500 mr-2" />
            ) : (
              <FolderTree className="h-4 w-4 text-blue-400 mr-2" />
            )}
          </>
        ) : (
          <>
            <span className="w-3.5 mr-1"></span>
            <span className="mr-2">{getFileIcon(node.name)}</span>
          </>
        )}
        <span className="text-sm truncate flex-1">{node.name}</span>
        {isSandpackFile && !isSandpackRoot && (
          <Badge variant="secondary" className="ml-2 text-xs">
            Sandpack
          </Badge>
        )}
      </div>

      {expanded && node.children && (
        <div>
          {node.children.map(child => (
            <EnhancedFileTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              onFileSelect={onFileSelect}
              selectedFilePath={selectedFilePath}
              expandedByDefault={expandedByDefault}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function FileBrowser({ onFileSelect, className }: FileBrowserProps) {
  // Get file store
  const {
    files,
    selectedFilePath,
    selectFile,
    addFile,
    addDirectory
  } = useFileStore()

  // Get Sandpack integration
  const {
    instances,
    getFiles: getSandpackFiles,
    setActiveInstance
  } = useSandpackIntegration()

  // State for search
  const [searchQuery, setSearchQuery] = useState("")
  
  // Convert flat file list to tree structure with Sandpack files
  const fileTree = React.useMemo(() => {
    // Create a map of paths to directories
    const dirMap: Record<string, FileNode> = {}

    // Create root directory
    const root: FileNode = {
      id: 'root',
      name: 'Project',
      type: 'directory',
      path: '/',
      children: []
    }
    dirMap['/'] = root

    // Create Sandpack section if there are instances
    if (instances.length > 0) {
      const sandpackRoot: FileNode = {
        id: 'sandpack-root',
        name: 'Sandpack',
        type: 'directory',
        path: '/sandpack',
        children: []
      }
      dirMap['/sandpack'] = sandpackRoot
      root.children?.push(sandpackRoot)

      // Add each Sandpack instance as a subdirectory
      instances.forEach(instance => {
        const instancePath = `/sandpack/${instance.name}`
        const instanceNode: FileNode = {
          id: `sandpack-instance-${instance.id}`,
          name: instance.name,
          type: 'directory',
          path: instancePath,
          children: []
        }
        dirMap[instancePath] = instanceNode
        sandpackRoot.children?.push(instanceNode)

        // Add Sandpack files for this instance
        const sandpackFiles = getSandpackFiles(instance.id)
        Object.entries(sandpackFiles).forEach(([filePath]) => {
          // Create file path within the instance directory
          const fullPath = `${instancePath}${filePath}`
          const fileName = filePath.split('/').pop() || filePath

          const fileNode: FileNode = {
            id: `sandpack-file-${instance.id}-${filePath}`,
            name: fileName,
            type: 'file',
            path: fullPath,
            extension: fileName.split('.').pop() || ''
          }

          // Add to instance directory
          instanceNode.children?.push(fileNode)
        })
      })
    }
    
    // Process all files
    files.forEach(file => {
      // Skip the root directory
      if (file.path === '/') return
      
      // Get the parent directory path
      const pathParts = file.path.split('/').filter(Boolean)
      const fileName = pathParts.pop() || ''
      const parentPath = pathParts.length === 0 ? '/' : '/' + pathParts.join('/')
      
      // Ensure parent directory exists
      if (!dirMap[parentPath]) {
        // Create parent directory
        const parentName = pathParts[pathParts.length - 1] || 'Project'
        const parentDir: FileNode = {
          id: `dir-${parentPath}`,
          name: parentName,
          type: 'directory',
          path: parentPath,
          children: []
        }
        dirMap[parentPath] = parentDir
        
        // Add to its parent
        const grandParentPath = pathParts.slice(0, -1).join('/')
        const grandParentDir = dirMap[grandParentPath] || root
        grandParentDir.children = grandParentDir.children || []
        grandParentDir.children.push(parentDir)
      }
      
      // Add file to parent directory
      if (file.type === 'file') {
        const parentDir = dirMap[parentPath]
        parentDir.children = parentDir.children || []
        parentDir.children.push({
          ...file,
          name: fileName
        })
      } else if (file.type === 'directory') {
        // Add directory to parent and to dirMap
        const dirPath = file.path.endsWith('/') ? file.path : `${file.path}/`
        if (!dirMap[dirPath]) {
          const dir: FileNode = {
            ...file,
            name: fileName,
            children: []
          }
          dirMap[dirPath] = dir
          
          const parentDir = dirMap[parentPath]
          parentDir.children = parentDir.children || []
          parentDir.children.push(dir)
        }
      }
    })
    
    // Sort each directory's children
    const sortDir = (dir: FileNode) => {
      if (dir.children) {
        dir.children.sort((a, b) => {
          if (a.type === 'directory' && b.type === 'file') return -1
          if (a.type === 'file' && b.type === 'directory') return 1
          return a.name.localeCompare(b.name)
        })
        
        dir.children.forEach(child => {
          if (child.type === 'directory') {
            sortDir(child)
          }
        })
      }
    }
    
    sortDir(root)
    
    return [root]
  }, [files])
  
  // Filter files based on search query
  const filteredFileTree = React.useMemo(() => {
    if (!searchQuery) return fileTree
    
    // Helper function to filter nodes
    const filterNode = (node: FileNode): FileNode | null => {
      if (node.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return node
      }
      
      if (node.type === 'directory' && node.children) {
        const filteredChildren = node.children
          .map(filterNode)
          .filter(Boolean) as FileNode[]
        
        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren
          }
        }
      }
      
      return null
    }
    
    return fileTree.map(filterNode).filter(Boolean) as FileNode[]
  }, [fileTree, searchQuery])
  
  // Handle file selection
  const handleFileSelect = (file: FileNode) => {
    if (file.type === 'file') {
      // Check if this is a Sandpack file
      if (file.path.startsWith('/sandpack/')) {
        // Extract instance name and original file path
        const pathParts = file.path.split('/')
        const instanceName = pathParts[2]
        const originalFilePath = '/' + pathParts.slice(3).join('/')

        // Find the instance
        const instance = instances.find(inst => inst.name === instanceName)
        if (instance) {
          // Set as active instance
          setActiveInstance(instance.id)

          // Get the file content from Sandpack
          const sandpackFiles = getSandpackFiles(instance.id)
          const fileContent = sandpackFiles[originalFilePath]

          if (fileContent) {
            // Add to regular file store for editing
            const content = typeof fileContent === 'string' ? fileContent : fileContent.code || ''
            addFile(file.path, content)
          }
        }
      }

      selectFile(file.path)
      onFileSelect(file)
    }
  }
  
  // Handle new file button click
  const handleNewFile = () => {
    const fileName = prompt('Enter file name:')
    if (fileName) {
      const path = selectedFilePath 
        ? `${selectedFilePath.substring(0, selectedFilePath.lastIndexOf('/'))}/${fileName}`
        : `/${fileName}`
      
      addFile(path, '')
    }
  }
  
  // Handle new folder button click
  const handleNewFolder = () => {
    const folderName = prompt('Enter folder name:')
    if (folderName) {
      const path = selectedFilePath 
        ? `${selectedFilePath.substring(0, selectedFilePath.lastIndexOf('/'))}/${folderName}`
        : `/${folderName}`
      
      addDirectory(path)
    }
  }
  
  return (
    <div className={cn("flex flex-col h-full border-r border-border", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-muted/30">
        <div className="flex items-center">
          <FolderTree className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="text-sm font-medium">Explorer</span>
        </div>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleNewFile}>
            <FilePlus className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleNewFolder}>
            <FolderPlus className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Search */}
      <div className="p-2 border-b border-border">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files"
            className="pl-8 h-9 text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      {/* File Tree */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-2">
            {filteredFileTree.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                <File className="h-8 w-8 mb-2 opacity-50" />
                <p className="text-sm">No files yet</p>
              </div>
            ) : (
              filteredFileTree.map(file => (
                <EnhancedFileTreeNode
                  key={file.id}
                  node={file}
                  level={0}
                  onFileSelect={handleFileSelect}
                  selectedFilePath={selectedFilePath || undefined}
                  expandedByDefault={true}
                />
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
