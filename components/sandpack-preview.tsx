/**
 * Sandpack Preview Component
 * 
 * Standalone preview component for Sandpack instances
 */

"use client";

import * as React from "react";
import { useState, useCallback, useEffect } from "react";
import { cn } from "@/lib/utils";
import {
  SandpackProvider,
  SandpackPreview,
  SandpackConsole,
  useSandpack
} from '@codesandbox/sandpack-react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Monitor,
  Terminal,
  RefreshCw,
  ExternalLink,
  Maximize2,
  Minimize2,
  Settings,
  Play,
  Square,
  AlertCircle,
  Loader2
} from "lucide-react";
import { useSandpackIntegration } from "@/lib/stores/sandpack-store";

interface SandpackPreviewProps {
  className?: string;
  projectId?: string;
  instanceId?: string;
  showConsole?: boolean;
  showNavigator?: boolean;
  showRefreshButton?: boolean;
  showOpenInCodeSandbox?: boolean;
  autoReload?: boolean;
  startRoute?: string;
}

export function SandpackPreview({
  className,
  projectId,
  instanceId,
  showConsole = true,
  showNavigator = true,
  showRefreshButton = true,
  showOpenInCodeSandbox = true,
  autoReload = true,
  startRoute = "/"
}: SandpackPreviewProps) {
  const {
    instances,
    activeInstance,
    isLoading,
    error,
    setActiveInstance
  } = useSandpackIntegration(projectId);

  const [activeTab, setActiveTab] = useState("preview");
  const [isMaximized, setIsMaximized] = useState(false);

  // Determine which instance to use
  const targetInstance = instanceId 
    ? instances.find(i => i.id === instanceId) 
    : activeInstance;

  // Set active instance if instanceId is provided
  useEffect(() => {
    if (instanceId && instances.length > 0) {
      const instance = instances.find(i => i.id === instanceId);
      if (instance && (!activeInstance || activeInstance.id !== instanceId)) {
        setActiveInstance(instanceId);
      }
    }
  }, [instanceId, instances, activeInstance, setActiveInstance]);

  if (error) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Preview Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{error.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading || !targetInstance) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Loading preview...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <SandpackProvider
        template={targetInstance.config.template}
        files={targetInstance.files}
        customSetup={{
          dependencies: targetInstance.config.dependencies || {},
          devDependencies: targetInstance.config.devDependencies || {}
        }}
        options={{
          autorun: targetInstance.config.autorun,
          autoReload: autoReload,
          startRoute: startRoute
        }}
      >
        <SandpackPreviewContent
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          isMaximized={isMaximized}
          setIsMaximized={setIsMaximized}
          showConsole={showConsole}
          showNavigator={showNavigator}
          showRefreshButton={showRefreshButton}
          showOpenInCodeSandbox={showOpenInCodeSandbox}
          targetInstance={targetInstance}
        />
      </SandpackProvider>
    </div>
  );
}

// Preview Content Component (must be inside SandpackProvider)
function SandpackPreviewContent({
  activeTab,
  setActiveTab,
  isMaximized,
  setIsMaximized,
  showConsole,
  showNavigator,
  showRefreshButton,
  showOpenInCodeSandbox,
  targetInstance
}: {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  isMaximized: boolean;
  setIsMaximized: (maximized: boolean) => void;
  showConsole: boolean;
  showNavigator: boolean;
  showRefreshButton: boolean;
  showOpenInCodeSandbox: boolean;
  targetInstance: any;
}) {
  const { sandpack } = useSandpack();

  const handleRefresh = useCallback(() => {
    if (sandpack.iframe) {
      sandpack.iframe.src = sandpack.iframe.src;
    }
  }, [sandpack.iframe]);

  const handleOpenInNewTab = useCallback(() => {
    if (sandpack.iframe && sandpack.iframe.src) {
      window.open(sandpack.iframe.src, '_blank');
    }
  }, [sandpack.iframe]);

  const tabs = [
    { id: "preview", label: "Preview", icon: <Monitor className="h-4 w-4" /> },
    ...(showConsole ? [{ id: "console", label: "Console", icon: <Terminal className="h-4 w-4" /> }] : [])
  ];

  return (
    <>
      {/* Header */}
      <div className="border-b border-border p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <span className={`w-2 h-2 rounded-full ${
                targetInstance.status === 'running' ? 'bg-green-500' :
                targetInstance.status === 'error' ? 'bg-red-500' :
                'bg-yellow-500'
              }`} />
              {targetInstance.name}
            </Badge>
            <Badge variant="secondary">{targetInstance.config.template}</Badge>
          </div>
          
          <div className="flex items-center gap-1">
            {showRefreshButton && (
              <Button size="sm" variant="ghost" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
            
            <Button size="sm" variant="ghost" onClick={handleOpenInNewTab}>
              <ExternalLink className="h-4 w-4" />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsMaximized(!isMaximized)}
            >
              {isMaximized ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1">
        {tabs.length > 1 ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-2">
              {tabs.map((tab) => (
                <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                  {tab.icon}
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="preview" className="flex-1 m-0">
              <SandpackPreview
                showNavigator={showNavigator}
                showRefreshButton={false} // We handle this in our header
                showOpenInCodeSandbox={showOpenInCodeSandbox}
                showOpenNewtab={false} // We handle this in our header
              />
            </TabsContent>

            {showConsole && (
              <TabsContent value="console" className="flex-1 m-0">
                <SandpackConsole
                  showHeader
                  showSyntaxError
                  showResetConsoleButton
                  showRestartButton
                />
              </TabsContent>
            )}
          </Tabs>
        ) : (
          // Single preview mode
          <SandpackPreview
            showNavigator={showNavigator}
            showRefreshButton={false}
            showOpenInCodeSandbox={showOpenInCodeSandbox}
            showOpenNewtab={false}
          />
        )}
      </div>

      {/* Status Bar */}
      <div className="border-t border-border p-2 bg-muted/50">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>Status: {targetInstance.status}</span>
            <span>Template: {targetInstance.config.template}</span>
          </div>
          <div className="flex items-center gap-2">
            <span>Last updated: {targetInstance.lastActivity.toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    </>
  );
}

// Standalone Preview Window Component
export function SandpackPreviewWindow({
  instanceId,
  projectId,
  className
}: {
  instanceId: string;
  projectId?: string;
  className?: string;
}) {
  return (
    <div className={cn("h-screen w-screen bg-background", className)}>
      <SandpackPreview
        instanceId={instanceId}
        projectId={projectId}
        showConsole={false}
        showNavigator={true}
        showRefreshButton={true}
        showOpenInCodeSandbox={true}
        className="h-full"
      />
    </div>
  );
}

// Mini Preview Component for thumbnails/cards
export function SandpackMiniPreview({
  instanceId,
  projectId,
  className,
  onClick
}: {
  instanceId: string;
  projectId?: string;
  className?: string;
  onClick?: () => void;
}) {
  const { instances } = useSandpackIntegration(projectId);
  const instance = instances.find(i => i.id === instanceId);

  if (!instance) {
    return (
      <div className={cn("aspect-video bg-muted rounded-lg flex items-center justify-center", className)}>
        <Monitor className="h-8 w-8 text-muted-foreground" />
      </div>
    );
  }

  return (
    <Card className={cn("aspect-video cursor-pointer hover:shadow-md transition-shadow", className)} onClick={onClick}>
      <CardContent className="p-0 h-full">
        <SandpackProvider
          template={instance.config.template}
          files={instance.files}
          customSetup={{
            dependencies: instance.config.dependencies || {},
            devDependencies: instance.config.devDependencies || {}
          }}
          options={{
            autorun: false // Don't auto-run for mini previews
          }}
        >
          <div className="h-full relative overflow-hidden rounded-lg">
            <SandpackPreview
              showNavigator={false}
              showRefreshButton={false}
              showOpenInCodeSandbox={false}
              showOpenNewtab={false}
            />
            <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors flex items-center justify-center">
              <Play className="h-8 w-8 text-white opacity-0 hover:opacity-100 transition-opacity" />
            </div>
          </div>
        </SandpackProvider>
      </CardContent>
    </Card>
  );
}
