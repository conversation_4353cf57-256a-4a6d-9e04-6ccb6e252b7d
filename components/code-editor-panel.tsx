"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { FileBrowser } from "@/components/code-editor/file-browser";
import { FileTabs } from "@/components/code-editor/file-tabs";
import { useOpenFilesStore } from "@/lib/stores/open-files-store";
import { useEditorFunctionsStore } from "@/lib/stores/editor-functions-store";
import {
  Save,
  Check,
  Copy,
  Code,
  FileCode,
  FolderClosed,
  Search,
  Network,
  AlertCircle,
  Loader2,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import MonacoEditor from "@monaco-editor/react";
import {
  ResizablePanelGroup,
  ResizablePanel,
  Resizable<PERSON><PERSON>le,
} from "@/components/ui/resizable";

interface CodeEditorPanelProps {
  className?: string;
  projectId?: string;
}

/**
 * CodeEditorPanel component
 *
 * A code editor panel that integrates with the editor functions store.
 * This component is designed to be used in the main workspace layout.
 */
export function CodeEditorPanel({
  className,
  projectId,
}: CodeEditorPanelProps) {
  // Get editor functions store
  const {
    copied,
    isAnalyzing,
    isBuildingKnowledgeGraph,
    knowledgeGraphData,
    analysisResults,
    operationStatus,
    selectedContext,
    activeTab,
    setActiveTab,
    handleFileSelect,
    handleTabClick,
    handleTabClose,
    handleEditorChange,
    handleCopyToClipboard,
    getFileContent,
    getFileLanguage,
    handleBuildKnowledgeGraph,
    handleAnalyzeCode,
    handleFileUpdate,
  } = useEditorFunctionsStore();

  // Get open files store
  const { openFiles, getActiveFile } = useOpenFilesStore();

  // Get active file
  const activeFile = getActiveFile();

  return (
    <div className={cn("flex h-full bg-background overflow-hidden", className)}>
      <ResizablePanelGroup direction="horizontal">
        {/* File Browser Panel */}
        <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
          <FileBrowser onFileSelect={handleFileSelect} className="h-full" />
        </ResizablePanel>

        <ResizableHandle withHandle />

        {/* Code Editor Panel */}
        <ResizablePanel defaultSize={80}>
          <div className="flex flex-col h-full">
            {/* File Tabs */}
            <div className="border-b border-border">
              <FileTabs
                openFiles={openFiles}
                activeFile={activeFile || undefined}
                onTabClick={handleTabClick}
                onTabClose={handleTabClose}
              />
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-auto">
              {/* Editor Tab */}
              {activeTab === "editor" && (
                <>
                  {activeFile ? (
                    <div className="relative h-full">
                      {/* Context Badge */}
                      {selectedContext.type !== "none" && (
                        <div className="absolute top-2 right-2 z-10">
                          <Badge
                            variant="outline"
                            className="bg-background/80 backdrop-blur-sm"
                          >
                            {selectedContext.type === "codebase" ? (
                              <>
                                <Code className="h-3 w-3 mr-1 text-primary" />
                                <span className="text-xs">
                                  Codebase Context
                                </span>
                              </>
                            ) : selectedContext.type === "file" ? (
                              <>
                                <FileCode className="h-3 w-3 mr-1 text-green-600 dark:text-green-400" />
                                <span className="text-xs">
                                  {selectedContext.path}
                                </span>
                              </>
                            ) : (
                              <>
                                <FolderClosed className="h-3 w-3 mr-1 text-amber-600 dark:text-amber-400" />
                                <span className="text-xs">
                                  {selectedContext.path}
                                </span>
                              </>
                            )}
                          </Badge>
                        </div>
                      )}

                      {/* Operation Status */}
                      {operationStatus.status !== "idle" && (
                        <div className="absolute top-2 left-2 z-10">
                          <Badge
                            variant={
                              operationStatus.status === "loading"
                                ? "outline"
                                : operationStatus.status === "success"
                                ? "default"
                                : "destructive"
                            }
                            className="bg-background/80 backdrop-blur-sm"
                          >
                            {operationStatus.status === "loading" && (
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            )}
                            {operationStatus.status === "success" && (
                              <Check className="h-3 w-3 mr-1" />
                            )}
                            {operationStatus.status === "error" && (
                              <AlertCircle className="h-3 w-3 mr-1" />
                            )}
                            <span className="text-xs">
                              {operationStatus.message}
                            </span>
                          </Badge>
                        </div>
                      )}

                      <MonacoEditor
                        language={getFileLanguage()}
                        theme="vs-dark" // Use vs-dark for dark mode, vs for light mode
                        value={getFileContent()}
                        options={{
                          minimap: { enabled: true },
                          scrollBeyondLastLine: false,
                          automaticLayout: true,
                          fontSize: 14,
                          lineNumbers: "on",
                          wordWrap: "on",
                          folding: true,
                          // Additional options to better match Shadcn UI theme
                          fontFamily:
                            "'JetBrains Mono', 'Fira Code', Menlo, Monaco, 'Courier New', monospace",
                          renderLineHighlight: "all",
                          cursorBlinking: "smooth",
                          smoothScrolling: true,
                          padding: { top: 16 },
                        }}
                        onChange={handleEditorChange}
                      />
                    </div>
                  ) : (
                    <div className="flex h-full items-center justify-center text-muted-foreground">
                      <p>Select a file to edit</p>
                    </div>
                  )}
                </>
              )}

              {/* Knowledge Graph Tab */}
              {activeTab === "knowledge-graph" && (
                <div className="h-full p-4">
                  {knowledgeGraphData ? (
                    <div className="h-full flex flex-col">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium">Knowledge Graph</h3>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-primary/10 text-primary"
                          >
                            {knowledgeGraphData.nodes.length} Nodes
                          </Badge>
                          <Badge
                            variant="outline"
                            className="bg-secondary/10 text-secondary-foreground"
                          >
                            {knowledgeGraphData.edges.length} Edges
                          </Badge>
                        </div>
                      </div>

                      <div className="flex-1 border rounded-md bg-muted/20 flex items-center justify-center">
                        <div className="text-center">
                          <Network className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                          <p className="text-muted-foreground">
                            Knowledge graph visualization would appear here
                          </p>
                          <p className="text-xs text-muted-foreground mt-2">
                            (In a real implementation, this would use D3.js or a
                            similar library)
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="h-full flex flex-col items-center justify-center">
                      <Network className="h-16 w-16 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground mb-4">
                        No knowledge graph data available
                      </p>
                      <Button
                        onClick={handleBuildKnowledgeGraph}
                        disabled={isBuildingKnowledgeGraph}
                      >
                        {isBuildingKnowledgeGraph ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Building...
                          </>
                        ) : (
                          <>
                            <Network className="h-4 w-4 mr-2" />
                            Build Knowledge Graph
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {/* Analysis Tab */}
              {activeTab === "analysis" && (
                <div className="h-full p-4">
                  {analysisResults ? (
                    <div className="h-full flex flex-col">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium">
                          Analysis Results: {analysisResults.file}
                        </h3>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-destructive/10 text-destructive"
                          >
                            {analysisResults.issues.length} Issues
                          </Badge>
                          <Badge
                            variant="outline"
                            className="bg-primary/10 text-primary"
                          >
                            {analysisResults.suggestions.length} Suggestions
                          </Badge>
                        </div>
                      </div>

                      <ScrollArea className="flex-1">
                        <div className="space-y-4">
                          {/* Issues */}
                          <div>
                            <h4 className="text-sm font-medium mb-2">Issues</h4>
                            <div className="space-y-2">
                              {analysisResults.issues.map(
                                (issue: any, index: number) => (
                                  <div
                                    key={index}
                                    className="p-2 border rounded-md bg-background"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Badge
                                        variant={
                                          issue.type === "warning"
                                            ? "outline"
                                            : "secondary"
                                        }
                                        className={
                                          issue.type === "warning"
                                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                                            : "bg-destructive/10 text-destructive"
                                        }
                                      >
                                        {issue.type}
                                      </Badge>
                                      <span className="text-sm">
                                        {issue.message}
                                      </span>
                                    </div>
                                    <div className="text-xs text-muted-foreground mt-1">
                                      Line {issue.line}, Column {issue.column}
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          </div>

                          {/* Suggestions */}
                          <div>
                            <h4 className="text-sm font-medium mb-2">
                              Suggestions
                            </h4>
                            <div className="space-y-2">
                              {analysisResults.suggestions.map(
                                (suggestion: any, index: number) => (
                                  <div
                                    key={index}
                                    className="p-2 border rounded-md bg-background"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Badge
                                        variant="outline"
                                        className="bg-primary/10 text-primary"
                                      >
                                        {suggestion.type}
                                      </Badge>
                                      <span className="text-sm">
                                        {suggestion.message}
                                      </span>
                                    </div>
                                    <div className="text-xs text-muted-foreground mt-1">
                                      Lines {suggestion.lines[0]}-
                                      {suggestion.lines[1]}
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          </div>

                          {/* Patterns */}
                          <div>
                            <h4 className="text-sm font-medium mb-2">
                              Detected Patterns
                            </h4>
                            <div className="space-y-2">
                              {analysisResults.patterns.map(
                                (pattern: any, index: number) => (
                                  <div
                                    key={index}
                                    className="p-2 border rounded-md bg-background"
                                  >
                                    <div className="flex items-center gap-2">
                                      <Badge
                                        variant="outline"
                                        className={
                                          pattern.type === "design-pattern"
                                            ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                            : "bg-destructive/10 text-destructive"
                                        }
                                      >
                                        {pattern.type}
                                      </Badge>
                                      <span className="text-sm">
                                        {pattern.name}
                                      </span>
                                      <Badge
                                        variant="outline"
                                        className="ml-auto bg-muted/50 text-muted-foreground"
                                      >
                                        {Math.round(pattern.confidence * 100)}%
                                        confidence
                                      </Badge>
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        </div>
                      </ScrollArea>
                    </div>
                  ) : (
                    <div className="h-full flex flex-col items-center justify-center">
                      <Search className="h-16 w-16 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground mb-4">
                        No analysis results available
                      </p>
                      <Button
                        onClick={handleAnalyzeCode}
                        disabled={!activeFile || isAnalyzing}
                      >
                        {isAnalyzing ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Analyzing...
                          </>
                        ) : (
                          <>
                            <Search className="h-4 w-4 mr-2" />
                            Analyze Current File
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Editor Actions and Tabs */}
            <div className="flex items-center justify-between border-t border-border bg-muted/50 h-10">
              <div className="flex">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`px-2 py-1 rounded-none ${activeTab === "editor" ? "bg-background" : ""}`}
                  onClick={() => setActiveTab("editor")}
                >
                  <FileCode className="h-3 w-3 mr-1" />
                  Editor
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`px-2 py-1 rounded-none ${activeTab === "knowledge-graph" ? "bg-background" : ""}`}
                  onClick={() => setActiveTab("knowledge-graph")}
                >
                  <Network className="h-3 w-3 mr-1" />
                  Knowledge Graph
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`px-2 py-1 rounded-none ${activeTab === "analysis" ? "bg-background" : ""}`}
                  onClick={() => setActiveTab("analysis")}
                >
                  <Search className="h-3 w-3 mr-1" />
                  Analysis
                </Button>
              </div>

              <div className="flex items-center gap-1 pr-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleAnalyzeCode}
                        disabled={!activeFile || isAnalyzing}
                      >
                        {isAnalyzing ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <Search className="h-3 w-3" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Analyze code</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleBuildKnowledgeGraph}
                        disabled={isBuildingKnowledgeGraph}
                      >
                        {isBuildingKnowledgeGraph ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <Network className="h-3 w-3" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Build knowledge graph</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCopyToClipboard}
                        disabled={!activeFile}
                      >
                        {copied ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy to clipboard</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          if (activeFile) {
                            handleFileUpdate(
                              activeFile.path,
                              getFileContent()
                            );
                          }
                        }}
                        disabled={!activeFile}
                      >
                        <Save className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Save file</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
