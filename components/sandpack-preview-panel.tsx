/**
 * Sandpack Preview Panel
 * 
 * A preview component that integrates with the existing code editor panel
 * using Sandpack hooks for live preview functionality
 */

"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import {
  SandpackProvider,
  SandpackPreview,
  SandpackConsole,
  SandpackCodeEditor,
  SandpackFileExplorer,
  useSandpack,
  useActiveCode,
  useSandpackNavigation,
  useSandpackConsole
} from '@codesandbox/sandpack-react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Monitor,
  Terminal,
  RefreshCw,
  ExternalLink,
  Maximize2,
  Minimize2,
  Settings,
  Play,
  Square,
  AlertCircle,
  Loader2,
  Code,
  FileText
} from "lucide-react";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";

interface SandpackPreviewPanelProps {
  className?: string;
  template?: string;
  files?: Record<string, string>;
  showConsole?: boolean;
  showFileExplorer?: boolean;
  autorun?: boolean;
  onCodeChange?: (code: string, path: string) => void;
}

export function SandpackPreviewPanel({
  className,
  template = "react",
  files = {},
  showConsole = true,
  showFileExplorer = true,
  autorun = true,
  onCodeChange
}: SandpackPreviewPanelProps) {
  return (
    <div className={cn("h-full", className)}>
      <SandpackProvider
        template={template as any}
        files={files}
        options={{
          autorun,
          autoReload: true
        }}
      >
        <SandpackPreviewContent
          showConsole={showConsole}
          showFileExplorer={showFileExplorer}
          onCodeChange={onCodeChange}
        />
      </SandpackProvider>
    </div>
  );
}

// Preview Content Component (must be inside SandpackProvider)
function SandpackPreviewContent({
  showConsole,
  showFileExplorer,
  onCodeChange
}: {
  showConsole: boolean;
  showFileExplorer: boolean;
  onCodeChange?: (code: string, path: string) => void;
}) {
  const { sandpack } = useSandpack();
  const { code } = useActiveCode();
  const [activeTab, setActiveTab] = React.useState("preview");
  const [isMaximized, setIsMaximized] = React.useState(false);

  // Notify parent of code changes
  React.useEffect(() => {
    if (onCodeChange && sandpack.activeFile) {
      onCodeChange(code, sandpack.activeFile);
    }
  }, [code, sandpack.activeFile, onCodeChange]);

  const handleRefresh = React.useCallback(() => {
    if (sandpack.iframe) {
      sandpack.iframe.src = sandpack.iframe.src;
    }
  }, [sandpack.iframe]);

  const handleOpenInNewTab = React.useCallback(() => {
    if (sandpack.iframe && sandpack.iframe.src) {
      window.open(sandpack.iframe.src, '_blank');
    }
  }, [sandpack.iframe]);

  if (isMaximized) {
    return (
      <div className="h-full flex flex-col">
        <div className="border-b border-border p-2 flex items-center justify-between">
          <h3 className="font-medium">Live Preview</h3>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{sandpack.status}</Badge>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsMaximized(false)}
            >
              <Minimize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex-1">
          <SandpackPreview
            showNavigator
            showRefreshButton
            showOpenInCodeSandbox
          />
        </div>
      </div>
    );
  }

  const tabs = [
    { id: "preview", label: "Preview", icon: <Monitor className="h-4 w-4" /> },
    ...(showConsole ? [{ id: "console", label: "Console", icon: <Terminal className="h-4 w-4" /> }] : []),
    { id: "editor", label: "Editor", icon: <Code className="h-4 w-4" /> }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-border p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <span className={`w-2 h-2 rounded-full ${
                sandpack.status === 'running' ? 'bg-green-500' :
                sandpack.status === 'error' ? 'bg-red-500' :
                'bg-yellow-500'
              }`} />
              {sandpack.status}
            </Badge>
            <Badge variant="secondary">{template}</Badge>
          </div>
          
          <div className="flex items-center gap-1">
            <Button size="sm" variant="ghost" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
            
            <Button size="sm" variant="ghost" onClick={handleOpenInNewTab}>
              <ExternalLink className="h-4 w-4" />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsMaximized(true)}
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            {tabs.map((tab) => (
              <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                {tab.icon}
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="preview" className="flex-1 m-0">
            <SandpackPreview
              showNavigator
              showRefreshButton={false} // We handle this in our header
              showOpenInCodeSandbox
              showOpenNewtab={false} // We handle this in our header
            />
          </TabsContent>

          <TabsContent value="editor" className="flex-1 m-0">
            {showFileExplorer ? (
              <ResizablePanelGroup direction="horizontal">
                <ResizablePanel defaultSize={30} minSize={20} maxSize={40}>
                  <div className="h-full border-r border-border">
                    <div className="p-2 border-b border-border">
                      <h4 className="text-sm font-medium">Files</h4>
                    </div>
                    <SandpackFileExplorer />
                  </div>
                </ResizablePanel>
                
                <ResizableHandle withHandle />
                
                <ResizablePanel defaultSize={70}>
                  <SandpackCodeEditor
                    showTabs
                    showLineNumbers
                    showInlineErrors
                    wrapContent
                  />
                </ResizablePanel>
              </ResizablePanelGroup>
            ) : (
              <SandpackCodeEditor
                showTabs
                showLineNumbers
                showInlineErrors
                wrapContent
              />
            )}
          </TabsContent>

          {showConsole && (
            <TabsContent value="console" className="flex-1 m-0">
              <SandpackConsole
                showHeader
                showSyntaxError
                showResetConsoleButton
                showRestartButton
              />
            </TabsContent>
          )}
        </Tabs>
      </div>

      {/* Status Bar */}
      <div className="border-t border-border p-2 bg-muted/50">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>Active: {sandpack.activeFile}</span>
            <span>Status: {sandpack.status}</span>
          </div>
          <div className="flex items-center gap-2">
            <span>Template: {template}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook for integrating Sandpack with existing editor
export function useSandpackIntegration(template: string = "react", initialFiles: Record<string, string> = {}) {
  const [files, setFiles] = React.useState(initialFiles);
  const [activeFile, setActiveFile] = React.useState<string | null>(null);

  const updateFile = React.useCallback((path: string, content: string) => {
    setFiles(prev => ({
      ...prev,
      [path]: content
    }));
  }, []);

  const createFile = React.useCallback((path: string, content: string = '') => {
    setFiles(prev => ({
      ...prev,
      [path]: content
    }));
  }, []);

  const deleteFile = React.useCallback((path: string) => {
    setFiles(prev => {
      const newFiles = { ...prev };
      delete newFiles[path];
      return newFiles;
    });
  }, []);

  return {
    files,
    activeFile,
    setActiveFile,
    updateFile,
    createFile,
    deleteFile,
    template
  };
}

// Standalone Preview Component for integration
export function SandpackPreviewIntegration({
  className,
  template = "react",
  files = {},
  onCodeChange
}: {
  className?: string;
  template?: string;
  files?: Record<string, string>;
  onCodeChange?: (code: string, path: string) => void;
}) {
  return (
    <Card className={cn("h-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Monitor className="h-5 w-5" />
          Live Preview
        </CardTitle>
      </CardHeader>
      <CardContent className="h-full p-0">
        <SandpackPreviewPanel
          template={template}
          files={files}
          showConsole={true}
          showFileExplorer={false}
          onCodeChange={onCodeChange}
          className="h-full"
        />
      </CardContent>
    </Card>
  );
}
