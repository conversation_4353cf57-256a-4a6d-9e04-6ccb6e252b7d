/**
 * Sandpack Runtime Types
 * 
 * Type definitions for the Sandpack-based development environment
 */

import { SandpackFiles, SandpackPredefinedTemplate } from '@codesandbox/sandpack-react';

// Sandpack Instance Configuration
export interface SandpackInstanceConfig {
  id: string;
  name: string;
  template: SandpackPredefinedTemplate;
  files?: SandpackFiles;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  projectId?: string;
  autorun?: boolean;
  autoReload?: boolean;
  showConsole?: boolean;
  showTests?: boolean;
  showFileExplorer?: boolean;
  theme?: string;
  environment?: Record<string, string>;
  customSetup?: {
    dependencies?: Record<string, string>;
    devDependencies?: Record<string, string>;
    entry?: string;
  };
}

// Sandpack Instance State
export interface SandpackInstance {
  id: string;
  name: string;
  config: SandpackInstanceConfig;
  status: SandpackStatus;
  files: SandpackFiles;
  activeFile: string;
  createdAt: Date;
  lastActivity: Date;
  metadata: Record<string, any>;
}

// Sandpack Status
export type SandpackStatus = 
  | 'idle'
  | 'initializing' 
  | 'running'
  | 'error'
  | 'stopped';

// Sandpack Template
export interface SandpackTemplate {
  id: string;
  name: string;
  description: string;
  template: SandpackPredefinedTemplate;
  files: SandpackFiles;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  tags: string[];
  framework: string;
  icon?: string;
  preview?: string;
}

// Sandpack Error
export interface SandpackError {
  code: string;
  message: string;
  instanceId?: string;
  timestamp: Date;
  stack?: string;
}

// Sandpack Console Output
export interface SandpackConsoleOutput {
  id: string;
  instanceId: string;
  type: 'log' | 'warn' | 'error' | 'info';
  message: string;
  timestamp: Date;
  data?: any[];
}

// Sandpack File Operations
export interface SandpackFileOperation {
  type: 'create' | 'update' | 'delete' | 'rename';
  path: string;
  content?: string;
  newPath?: string;
  timestamp: Date;
}

// Sandpack Settings
export interface SandpackSettings {
  theme: string;
  fontSize: number;
  tabSize: number;
  wordWrap: boolean;
  lineNumbers: boolean;
  minimap: boolean;
  autoSave: boolean;
  autoFormat: boolean;
  showInlineErrors: boolean;
  showConsole: boolean;
  showTests: boolean;
  showFileExplorer: boolean;
  autoReload: boolean;
  clearConsoleOnReload: boolean;
}

// Default Sandpack Settings
export const DEFAULT_SANDPACK_SETTINGS: SandpackSettings = {
  theme: 'auto',
  fontSize: 14,
  tabSize: 2,
  wordWrap: true,
  lineNumbers: true,
  minimap: false,
  autoSave: true,
  autoFormat: true,
  showInlineErrors: true,
  showConsole: true,
  showTests: false,
  showFileExplorer: true,
  autoReload: true,
  clearConsoleOnReload: true,
};

// Sandpack API Response
export interface SandpackAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

// Sandpack Event Types
export type SandpackEventType = 
  | 'instance:created'
  | 'instance:updated'
  | 'instance:deleted'
  | 'instance:started'
  | 'instance:stopped'
  | 'instance:error'
  | 'file:created'
  | 'file:updated'
  | 'file:deleted'
  | 'console:output'
  | 'test:result';

// Sandpack Event
export interface SandpackEvent {
  type: SandpackEventType;
  instanceId: string;
  timestamp: Date;
  data: any;
}

// Sandpack Test Result
export interface SandpackTestResult {
  id: string;
  instanceId: string;
  name: string;
  status: 'pass' | 'fail' | 'skip';
  duration: number;
  error?: string;
  timestamp: Date;
}

// Sandpack Package Installation
export interface SandpackPackageInstallation {
  instanceId: string;
  packages: string[];
  type: 'dependencies' | 'devDependencies';
  status: 'pending' | 'installing' | 'completed' | 'failed';
  error?: string;
  timestamp: Date;
}

// Sandpack Preview Options
export interface SandpackPreviewOptions {
  showNavigator?: boolean;
  showRefreshButton?: boolean;
  showOpenInCodeSandbox?: boolean;
  showOpenNewtab?: boolean;
  startRoute?: string;
  customActions?: React.ReactNode;
}

// Sandpack Editor Options
export interface SandpackEditorOptions {
  showTabs?: boolean;
  showLineNumbers?: boolean;
  showInlineErrors?: boolean;
  showRunButton?: boolean;
  wrapContent?: boolean;
  closableTabs?: boolean;
  readOnly?: boolean;
  extensions?: any[];
  extensionsKeymap?: any[];
}

// Sandpack Console Options
export interface SandpackConsoleOptions {
  showHeader?: boolean;
  showSyntaxError?: boolean;
  showResetConsoleButton?: boolean;
  showRestartButton?: boolean;
  maxMessageCount?: number;
  resetOnPreviewRestart?: boolean;
  standalone?: boolean;
}

// Sandpack File Explorer Options
export interface SandpackFileExplorerOptions {
  autoHiddenFiles?: boolean;
  initialCollapsedFolder?: string[];
}

// Sandpack Layout Configuration
export interface SandpackLayoutConfig {
  showFileExplorer: boolean;
  showEditor: boolean;
  showPreview: boolean;
  showConsole: boolean;
  showTests: boolean;
  layout: 'horizontal' | 'vertical' | 'stacked';
  panelSizes?: {
    fileExplorer?: number;
    editor?: number;
    preview?: number;
    console?: number;
  };
}

// Sandpack Integration Hook Return Type
export interface SandpackIntegrationHook {
  instances: SandpackInstance[];
  activeInstance: SandpackInstance | null;
  isLoading: boolean;
  error: SandpackError | null;
  createInstance: (config: SandpackInstanceConfig) => Promise<string>;
  updateInstance: (id: string, updates: Partial<SandpackInstanceConfig>) => Promise<void>;
  deleteInstance: (id: string) => Promise<void>;
  setActiveInstance: (id: string | null) => void;
  updateFile: (instanceId: string, path: string, content: string) => Promise<void>;
  deleteFile: (instanceId: string, path: string) => Promise<void>;
  installPackages: (instanceId: string, packages: string[], dev?: boolean) => Promise<void>;
  runTests: (instanceId: string) => Promise<SandpackTestResult[]>;
  clearConsole: (instanceId: string) => void;
  resetInstance: (instanceId: string) => Promise<void>;
}
