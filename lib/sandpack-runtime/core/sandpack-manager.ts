/**
 * Sandpack Manager
 * 
 * Core manager for Sandpack instances and operations
 */

import { EventEmitter } from 'events';
import { 
  SandpackInstance, 
  SandpackInstanceConfig, 
  SandpackError, 
  SandpackStatus,
  SandpackConsoleOutput,
  SandpackTestResult,
  SandpackFileOperation
} from '../types';
import { getTemplateById } from '../templates';

export class SandpackManager extends EventEmitter {
  private instances: Map<string, SandpackInstance> = new Map();
  private isInitialized = false;

  constructor() {
    super();
    this.setMaxListeners(100);
  }

  /**
   * Initialize the Sandpack manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Sandpack manager initialized successfully');
      this.isInitialized = true;
      this.emit('manager:initialized');
    } catch (error) {
      console.error('Sandpack manager initialization failed:', error);
      throw error;
    }
  }

  /**
   * Create a new Sandpack instance
   */
  async createInstance(config: SandpackInstanceConfig): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const instanceId = this.generateInstanceId();
      
      // Get template if specified
      const template = getTemplateById(config.template);
      if (!template) {
        throw new Error(`Template '${config.template}' not found`);
      }

      // Merge template files with provided files
      const files = {
        ...template.files,
        ...config.files
      };

      const instance: SandpackInstance = {
        id: instanceId,
        name: config.name,
        config,
        status: 'idle',
        files,
        activeFile: Object.keys(files)[0] || '/App.js',
        createdAt: new Date(),
        lastActivity: new Date(),
        metadata: {}
      };

      this.instances.set(instanceId, instance);
      this.emit('instance:created', instanceId, { config });

      console.log(`Sandpack instance created: ${instanceId} (${config.name})`);
      return instanceId;
    } catch (error) {
      console.error('Sandpack instance creation failed:', error);
      const sandpackError: SandpackError = {
        code: 'INSTANCE_CREATION_FAILED',
        message: `Failed to create instance: ${error instanceof Error ? error.message : 'Unknown error'}`,
        instanceId: config.name,
        timestamp: new Date()
      };
      this.emit('instance:error', config.name, sandpackError);
      throw sandpackError;
    }
  }

  /**
   * Get instance by ID
   */
  getInstance(instanceId: string): SandpackInstance | null {
    return this.instances.get(instanceId) || null;
  }

  /**
   * List all instances
   */
  listInstances(): SandpackInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * Update instance configuration
   */
  async updateInstance(instanceId: string, updates: Partial<SandpackInstanceConfig>): Promise<void> {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    try {
      instance.config = { ...instance.config, ...updates };
      instance.lastActivity = new Date();
      
      this.emit('instance:updated', instanceId, { updates });
    } catch (error) {
      throw new Error(`Failed to update instance: ${error}`);
    }
  }

  /**
   * Delete instance
   */
  async deleteInstance(instanceId: string): Promise<void> {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    try {
      instance.status = 'stopped';
      this.instances.delete(instanceId);
      
      this.emit('instance:deleted', instanceId);
    } catch (error) {
      throw new Error(`Failed to delete instance: ${error}`);
    }
  }

  /**
   * Update file in instance
   */
  async updateFile(instanceId: string, path: string, content: string): Promise<void> {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    try {
      instance.files[path] = { code: content };
      instance.lastActivity = new Date();
      
      const operation: SandpackFileOperation = {
        type: 'update',
        path,
        content,
        timestamp: new Date()
      };
      
      this.emit('file:updated', instanceId, operation);
    } catch (error) {
      throw new Error(`Failed to update file ${path}: ${error}`);
    }
  }

  /**
   * Create file in instance
   */
  async createFile(instanceId: string, path: string, content: string = ''): Promise<void> {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    try {
      if (instance.files[path]) {
        throw new Error(`File ${path} already exists`);
      }

      instance.files[path] = { code: content };
      instance.lastActivity = new Date();
      
      const operation: SandpackFileOperation = {
        type: 'create',
        path,
        content,
        timestamp: new Date()
      };
      
      this.emit('file:created', instanceId, operation);
    } catch (error) {
      throw new Error(`Failed to create file ${path}: ${error}`);
    }
  }

  /**
   * Delete file from instance
   */
  async deleteFile(instanceId: string, path: string): Promise<void> {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    try {
      if (!instance.files[path]) {
        throw new Error(`File ${path} not found`);
      }

      delete instance.files[path];
      instance.lastActivity = new Date();
      
      // Update active file if it was deleted
      if (instance.activeFile === path) {
        const remainingFiles = Object.keys(instance.files);
        instance.activeFile = remainingFiles[0] || '';
      }
      
      const operation: SandpackFileOperation = {
        type: 'delete',
        path,
        timestamp: new Date()
      };
      
      this.emit('file:deleted', instanceId, operation);
    } catch (error) {
      throw new Error(`Failed to delete file ${path}: ${error}`);
    }
  }

  /**
   * Set active file
   */
  setActiveFile(instanceId: string, path: string): void {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    if (!instance.files[path]) {
      throw new Error(`File ${path} not found`);
    }

    instance.activeFile = path;
    instance.lastActivity = new Date();
  }

  /**
   * Install packages in instance
   */
  async installPackages(instanceId: string, packages: string[], dev: boolean = false): Promise<void> {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    try {
      // Update package.json if it exists
      const packageJsonPath = '/package.json';
      if (instance.files[packageJsonPath]) {
        const packageJson = JSON.parse(instance.files[packageJsonPath].code);
        const depKey = dev ? 'devDependencies' : 'dependencies';
        
        if (!packageJson[depKey]) {
          packageJson[depKey] = {};
        }
        
        packages.forEach(pkg => {
          // Simple version assignment - in real implementation, you'd fetch latest versions
          packageJson[depKey][pkg] = 'latest';
        });
        
        instance.files[packageJsonPath] = { 
          code: JSON.stringify(packageJson, null, 2) 
        };
      }
      
      instance.lastActivity = new Date();
      this.emit('packages:installed', instanceId, { packages, dev });
    } catch (error) {
      throw new Error(`Failed to install packages: ${error}`);
    }
  }

  /**
   * Reset instance to original state
   */
  async resetInstance(instanceId: string): Promise<void> {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    try {
      // Get original template files
      const template = getTemplateById(instance.config.template);
      if (template) {
        instance.files = { ...template.files };
        instance.activeFile = Object.keys(instance.files)[0] || '';
      }
      
      instance.status = 'idle';
      instance.lastActivity = new Date();
      
      this.emit('instance:reset', instanceId);
    } catch (error) {
      throw new Error(`Failed to reset instance: ${error}`);
    }
  }

  /**
   * Update instance status
   */
  updateInstanceStatus(instanceId: string, status: SandpackStatus): void {
    const instance = this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    instance.status = status;
    instance.lastActivity = new Date();
    
    this.emit('instance:status_changed', instanceId, { status });
  }

  /**
   * Cleanup all instances
   */
  async cleanup(): Promise<void> {
    const instanceIds = Array.from(this.instances.keys());
    
    for (const instanceId of instanceIds) {
      try {
        await this.deleteInstance(instanceId);
      } catch (error) {
        console.error(`Failed to cleanup instance ${instanceId}:`, error);
      }
    }
    
    this.instances.clear();
    this.emit('manager:cleanup');
  }

  // Private helper methods
  private generateInstanceId(): string {
    return `sp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const sandpackManager = new SandpackManager();
