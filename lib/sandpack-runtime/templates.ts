/**
 * Sandpack Templates
 * 
 * Predefined templates for different frameworks and use cases
 */

import { SandpackTemplate } from './types';

export const SANDPACK_TEMPLATES: SandpackTemplate[] = [
  {
    id: 'react',
    name: 'React',
    description: 'React application with modern setup',
    template: 'react',
    files: {},
    dependencies: {
      'react': '^18.0.0',
      'react-dom': '^18.0.0'
    },
    tags: ['react', 'frontend', 'javascript'],
    framework: 'React',
    icon: '⚛️',
  },
  {
    id: 'react-ts',
    name: 'React TypeScript',
    description: 'React application with TypeScript',
    template: 'react-ts',
    files: {},
    dependencies: {
      'react': '^18.0.0',
      'react-dom': '^18.0.0',
      'typescript': '^4.0.0'
    },
    devDependencies: {
      '@types/react': '^18.0.0',
      '@types/react-dom': '^18.0.0'
    },
    tags: ['react', 'typescript', 'frontend'],
    framework: 'React',
    icon: '⚛️',
  },
  {
    id: 'vue',
    name: 'Vue.js',
    description: 'Vue.js application',
    template: 'vue',
    files: {},
    dependencies: {
      'vue': '^3.0.0'
    },
    tags: ['vue', 'frontend', 'javascript'],
    framework: 'Vue',
    icon: '🟢',
  },
  {
    id: 'vue-ts',
    name: 'Vue TypeScript',
    description: 'Vue.js application with TypeScript',
    template: 'vue-ts',
    files: {},
    dependencies: {
      'vue': '^3.0.0',
      'typescript': '^4.0.0'
    },
    tags: ['vue', 'typescript', 'frontend'],
    framework: 'Vue',
    icon: '🟢',
  },
  {
    id: 'angular',
    name: 'Angular',
    description: 'Angular application',
    template: 'angular',
    files: {},
    dependencies: {
      '@angular/core': '^15.0.0',
      '@angular/common': '^15.0.0',
      '@angular/platform-browser': '^15.0.0'
    },
    tags: ['angular', 'typescript', 'frontend'],
    framework: 'Angular',
    icon: '🅰️',
  },
  {
    id: 'svelte',
    name: 'Svelte',
    description: 'Svelte application',
    template: 'svelte',
    files: {},
    dependencies: {
      'svelte': '^3.0.0'
    },
    tags: ['svelte', 'frontend', 'javascript'],
    framework: 'Svelte',
    icon: '🧡',
  },
  {
    id: 'vanilla',
    name: 'Vanilla JavaScript',
    description: 'Plain JavaScript application',
    template: 'vanilla',
    files: {},
    dependencies: {},
    tags: ['vanilla', 'javascript', 'basic'],
    framework: 'Vanilla',
    icon: '📄',
  },
  {
    id: 'vanilla-ts',
    name: 'Vanilla TypeScript',
    description: 'Plain TypeScript application',
    template: 'vanilla-ts',
    files: {},
    dependencies: {
      'typescript': '^4.0.0'
    },
    tags: ['vanilla', 'typescript', 'basic'],
    framework: 'Vanilla',
    icon: '📄',
  },
  {
    id: 'nextjs',
    name: 'Next.js',
    description: 'Next.js React framework',
    template: 'nextjs',
    files: {},
    dependencies: {
      'next': '^13.0.0',
      'react': '^18.0.0',
      'react-dom': '^18.0.0'
    },
    tags: ['nextjs', 'react', 'fullstack'],
    framework: 'Next.js',
    icon: '▲',
  },
  {
    id: 'vite',
    name: 'Vite',
    description: 'Vite build tool with React',
    template: 'vite-react',
    files: {},
    dependencies: {
      'react': '^18.0.0',
      'react-dom': '^18.0.0',
      'vite': '^4.0.0'
    },
    tags: ['vite', 'react', 'build-tool'],
    framework: 'Vite',
    icon: '⚡',
  },
  {
    id: 'vite-ts',
    name: 'Vite TypeScript',
    description: 'Vite build tool with React and TypeScript',
    template: 'vite-react-ts',
    files: {},
    dependencies: {
      'react': '^18.0.0',
      'react-dom': '^18.0.0',
      'vite': '^4.0.0',
      'typescript': '^4.0.0'
    },
    devDependencies: {
      '@types/react': '^18.0.0',
      '@types/react-dom': '^18.0.0'
    },
    tags: ['vite', 'react', 'typescript', 'build-tool'],
    framework: 'Vite',
    icon: '⚡',
  },
  {
    id: 'node',
    name: 'Node.js',
    description: 'Node.js server application',
    template: 'node',
    files: {},
    dependencies: {
      'express': '^4.18.0'
    },
    tags: ['nodejs', 'backend', 'server'],
    framework: 'Node.js',
    icon: '🟢',
  },
  {
    id: 'test-ts',
    name: 'Testing with TypeScript',
    description: 'TypeScript project with Jest testing',
    template: 'test-ts',
    files: {},
    dependencies: {
      'typescript': '^4.0.0'
    },
    devDependencies: {
      'jest': '^29.0.0',
      '@types/jest': '^29.0.0'
    },
    tags: ['testing', 'typescript', 'jest'],
    framework: 'Testing',
    icon: '🧪',
  },
];

/**
 * Get all available templates
 */
export function getAvailableTemplates(): SandpackTemplate[] {
  return SANDPACK_TEMPLATES;
}

/**
 * Get template by ID
 */
export function getTemplateById(id: string): SandpackTemplate | undefined {
  return SANDPACK_TEMPLATES.find(template => template.id === id);
}

/**
 * Get templates by framework
 */
export function getTemplatesByFramework(framework: string): SandpackTemplate[] {
  return SANDPACK_TEMPLATES.filter(template => 
    template.framework.toLowerCase() === framework.toLowerCase()
  );
}

/**
 * Get templates by tag
 */
export function getTemplatesByTag(tag: string): SandpackTemplate[] {
  return SANDPACK_TEMPLATES.filter(template => 
    template.tags.includes(tag.toLowerCase())
  );
}

/**
 * Search templates by name or description
 */
export function searchTemplates(query: string): SandpackTemplate[] {
  const lowercaseQuery = query.toLowerCase();
  return SANDPACK_TEMPLATES.filter(template => 
    template.name.toLowerCase().includes(lowercaseQuery) ||
    template.description.toLowerCase().includes(lowercaseQuery) ||
    template.tags.some(tag => tag.includes(lowercaseQuery))
  );
}

/**
 * Get popular templates (most commonly used)
 */
export function getPopularTemplates(): SandpackTemplate[] {
  const popularIds = ['react', 'react-ts', 'vue', 'nextjs', 'vanilla'];
  return SANDPACK_TEMPLATES.filter(template => popularIds.includes(template.id));
}

/**
 * Get frontend templates
 */
export function getFrontendTemplates(): SandpackTemplate[] {
  return SANDPACK_TEMPLATES.filter(template => 
    template.tags.includes('frontend') || 
    ['React', 'Vue', 'Angular', 'Svelte', 'Vanilla'].includes(template.framework)
  );
}

/**
 * Get backend templates
 */
export function getBackendTemplates(): SandpackTemplate[] {
  return SANDPACK_TEMPLATES.filter(template => 
    template.tags.includes('backend') || 
    template.tags.includes('server') ||
    template.framework === 'Node.js'
  );
}

/**
 * Get fullstack templates
 */
export function getFullstackTemplates(): SandpackTemplate[] {
  return SANDPACK_TEMPLATES.filter(template => 
    template.tags.includes('fullstack') ||
    template.framework === 'Next.js'
  );
}
