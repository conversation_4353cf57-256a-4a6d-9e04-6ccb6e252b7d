/**
 * Sandpack Workspace Component
 * 
 * Main workspace component for Sandpack development environment
 */

'use client';

import React, { useState } from 'react';
import {
  SandpackProvider,
  SandpackLayout,
  SandpackCodeEditor,
  SandpackPreview,
  SandpackConsole,
  SandpackFileExplorer,
  SandpackTests
} from '@codesandbox/sandpack-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Settings, 
  FileText, 
  Terminal, 
  TestTube,
  Eye,
  Code,
  FolderOpen,
  AlertCircle
} from 'lucide-react';
import { useSandpackIntegration } from '@/lib/stores/sandpack-store';
import { SandpackInstanceConfig } from '../types';
import { getAvailableTemplates } from '../templates';

interface SandpackWorkspaceProps {
  projectId?: string;
  className?: string;
}

export function SandpackWorkspace({ projectId, className }: SandpackWorkspaceProps) {
  const {
    instances,
    activeInstance,
    isLoading,
    error,
    createInstance,
    setActiveInstance,
    deleteInstance,
    resetInstance
  } = useSandpackIntegration(projectId);

  const [activeTab, setActiveTab] = useState('instances');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [instanceName, setInstanceName] = useState('');

  const templates = getAvailableTemplates();

  const handleCreateInstance = async () => {
    if (!selectedTemplate || !instanceName) return;

    try {
      const template = templates.find(t => t.id === selectedTemplate);
      if (!template) return;

      const config: SandpackInstanceConfig = {
        id: '',
        name: instanceName,
        template: template.template,
        files: template.files,
        dependencies: template.dependencies,
        devDependencies: template.devDependencies,
        projectId,
        autorun: true,
        showConsole: true,
        showFileExplorer: true
      };

      await createInstance(config);
      setInstanceName('');
      setSelectedTemplate('');
      setActiveTab('instances');
    } catch (error) {
      console.error('Failed to create Sandpack instance:', error);
    }
  };

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Sandpack Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{error.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`h-full flex flex-col ${className}`}>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="instances">Instances</TabsTrigger>
          <TabsTrigger value="create">Create</TabsTrigger>
          <TabsTrigger value="workspace">Workspace</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="instances" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                Sandpack Instances
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-sm text-muted-foreground">Loading instances...</p>
                </div>
              ) : instances.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No Sandpack instances yet</p>
                  <p className="text-sm text-muted-foreground">Create your first instance to get started</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {instances.map((instance) => (
                    <div
                      key={instance.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        activeInstance?.id === instance.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => setActiveInstance(instance.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium">{instance.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Template: {instance.config.template}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={instance.status === 'running' ? 'default' : 'secondary'}>
                            {instance.status}
                          </Badge>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                resetInstance(instance.id);
                              }}
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteInstance(instance.id);
                              }}
                            >
                              <Square className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Create New Instance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Instance Name</label>
                <input
                  type="text"
                  value={instanceName}
                  onChange={(e) => setInstanceName(e.target.value)}
                  placeholder="Enter instance name"
                  className="w-full mt-1 px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Template</label>
                <select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="">Select a template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.icon} {template.name} - {template.description}
                    </option>
                  ))}
                </select>
              </div>

              <Button
                onClick={handleCreateInstance}
                disabled={!selectedTemplate || !instanceName || isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Create Instance
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workspace" className="space-y-4">
          {activeInstance ? (
            <SandpackWorkspaceContent instance={activeInstance} />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Code className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No active instance</p>
                <p className="text-sm text-muted-foreground">Select an instance to start coding</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Sandpack Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Settings panel coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Workspace content component for active instance
function SandpackWorkspaceContent({ instance }: { instance: any }) {
  const [workspaceTab, setWorkspaceTab] = useState('editor');

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            {instance.name}
          </CardTitle>
          <Badge variant="outline">{instance.config.template}</Badge>
        </div>
      </CardHeader>
      <CardContent className="h-full">
        <SandpackProvider
          template={instance.config.template}
          files={instance.files}
          customSetup={{
            dependencies: instance.config.dependencies || {},
            devDependencies: instance.config.devDependencies || {}
          }}
          options={{
            autorun: instance.config.autorun,
            autoReload: instance.config.autoReload
          }}
        >
          <Tabs value={workspaceTab} onValueChange={setWorkspaceTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="editor">
                <Code className="h-4 w-4 mr-2" />
                Editor
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="console">
                <Terminal className="h-4 w-4 mr-2" />
                Console
              </TabsTrigger>
              <TabsTrigger value="tests">
                <TestTube className="h-4 w-4 mr-2" />
                Tests
              </TabsTrigger>
            </TabsList>

            <TabsContent value="editor" className="h-full">
              <SandpackLayout>
                <SandpackFileExplorer />
                <SandpackCodeEditor 
                  showTabs
                  showLineNumbers
                  showInlineErrors
                  wrapContent
                />
              </SandpackLayout>
            </TabsContent>

            <TabsContent value="preview" className="h-full">
              <SandpackPreview 
                showNavigator
                showRefreshButton
                showOpenInCodeSandbox
              />
            </TabsContent>

            <TabsContent value="console" className="h-full">
              <SandpackConsole 
                showHeader
                showSyntaxError
                showResetConsoleButton
              />
            </TabsContent>

            <TabsContent value="tests" className="h-full">
              <SandpackTests />
            </TabsContent>
          </Tabs>
        </SandpackProvider>
      </CardContent>
    </Card>
  );
}
