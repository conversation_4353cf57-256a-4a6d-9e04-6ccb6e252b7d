/**
 * Sandpack Workspace Integration
 * 
 * Complete Sandpack workspace integration for the AI Node.js workspace
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Settings, 
  FileText, 
  Terminal, 
  TestTube,
  Eye,
  Code,
  FolderOpen,
  AlertCircle,
  Plus,
  Trash2,
  Bot,
  Wand2
} from 'lucide-react';
import { useSandpackIntegration } from '@/lib/stores/sandpack-store';
import { SandpackWorkspace } from './sandpack-workspace';
import { SandpackAIEditor } from './sandpack-ai-editor';
import { SandpackTemplateSelector } from './sandpack-template-selector';
import { AgenticChatInterface } from '@/components/agentic-chatbot';

interface SandpackWorkspaceIntegrationProps {
  projectId?: string;
  className?: string;
}

export function SandpackWorkspaceIntegration({ 
  projectId, 
  className 
}: SandpackWorkspaceIntegrationProps) {
  const {
    instances,
    activeInstance,
    isLoading,
    error,
    createInstance,
    updateInstance,
    deleteInstance,
    setActiveInstance,
    resetInstance,
    updateFile,
    installPackages
  } = useSandpackIntegration(projectId);

  const [activeTab, setActiveTab] = useState('instances');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  // Handle AI requests for code generation and assistance
  const handleAIRequest = useCallback(async (prompt: string, context: any) => {
    // This would integrate with your AI API
    // For now, return a mock response
    return `AI Response for: ${prompt}\n\nContext: ${JSON.stringify(context, null, 2)}`;
  }, []);

  // Handle file updates from AI editor
  const handleFileUpdate = useCallback(async (path: string, content: string) => {
    if (activeInstance) {
      try {
        await updateFile(activeInstance.id, path, content);
      } catch (error) {
        console.error('Failed to update file:', error);
      }
    }
  }, [activeInstance, updateFile]);

  // Handle instance creation from template selector
  const handleCreateFromTemplate = useCallback(async (config: any) => {
    try {
      await createInstance(config);
      setShowTemplateSelector(false);
      setActiveTab('workspace');
    } catch (error) {
      console.error('Failed to create instance:', error);
    }
  }, [createInstance]);

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Sandpack Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{error.message}</p>
            <Button 
              className="mt-4 w-full" 
              onClick={() => window.location.reload()}
            >
              Reload
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`h-full flex flex-col ${className}`}>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="border-b border-border">
          <div className="flex items-center justify-between p-4">
            <TabsList className="grid grid-cols-5 w-auto">
              <TabsTrigger value="instances">
                <FolderOpen className="h-4 w-4 mr-2" />
                Instances
              </TabsTrigger>
              <TabsTrigger value="workspace">
                <Code className="h-4 w-4 mr-2" />
                Workspace
              </TabsTrigger>
              <TabsTrigger value="ai-editor">
                <Bot className="h-4 w-4 mr-2" />
                AI Editor
              </TabsTrigger>
              <TabsTrigger value="templates">
                <FileText className="h-4 w-4 mr-2" />
                Templates
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              {activeInstance && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <span className={`w-2 h-2 rounded-full ${
                    activeInstance.status === 'running' ? 'bg-green-500' :
                    activeInstance.status === 'error' ? 'bg-red-500' :
                    'bg-yellow-500'
                  }`} />
                  {activeInstance.name}
                </Badge>
              )}
              <Button
                size="sm"
                onClick={() => {
                  setShowTemplateSelector(true);
                  setActiveTab('templates');
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                New Instance
              </Button>
            </div>
          </div>
        </div>

        <TabsContent value="instances" className="flex-1 p-0">
          <InstancesPanel
            instances={instances}
            activeInstance={activeInstance}
            isLoading={isLoading}
            onSelectInstance={setActiveInstance}
            onDeleteInstance={deleteInstance}
            onResetInstance={resetInstance}
            onCreateNew={() => {
              setShowTemplateSelector(true);
              setActiveTab('templates');
            }}
          />
        </TabsContent>

        <TabsContent value="workspace" className="flex-1 p-0">
          {activeInstance ? (
            <SandpackWorkspace
              projectId={projectId}
              className="h-full"
            />
          ) : (
            <EmptyState
              icon={<Code className="h-12 w-12" />}
              title="No Active Instance"
              description="Select an instance or create a new one to start coding"
              action={
                <Button onClick={() => {
                  setShowTemplateSelector(true);
                  setActiveTab('templates');
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Instance
                </Button>
              }
            />
          )}
        </TabsContent>

        <TabsContent value="ai-editor" className="flex-1 p-0">
          {activeInstance ? (
            <SandpackAIEditor
              instance={activeInstance}
              onFileUpdate={handleFileUpdate}
              onAIRequest={handleAIRequest}
              className="h-full"
            />
          ) : (
            <EmptyState
              icon={<Bot className="h-12 w-12" />}
              title="No Active Instance"
              description="Select an instance to use the AI-powered editor"
              action={
                <Button onClick={() => setActiveTab('instances')}>
                  <FolderOpen className="h-4 w-4 mr-2" />
                  View Instances
                </Button>
              }
            />
          )}
        </TabsContent>

        <TabsContent value="templates" className="flex-1 p-0">
          <SandpackTemplateSelector
            onCreateInstance={handleCreateFromTemplate}
            projectId={projectId}
            className="h-full"
          />
        </TabsContent>

        <TabsContent value="settings" className="flex-1 p-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Sandpack Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Editor Settings</h4>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Show line numbers</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Show inline errors</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Word wrap</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Runtime Settings</h4>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Auto-run on changes</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Show console</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" />
                      <span className="text-sm">Show tests</span>
                    </label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Instances Panel Component
function InstancesPanel({
  instances,
  activeInstance,
  isLoading,
  onSelectInstance,
  onDeleteInstance,
  onResetInstance,
  onCreateNew
}: {
  instances: any[];
  activeInstance: any;
  isLoading: boolean;
  onSelectInstance: (id: string) => void;
  onDeleteInstance: (id: string) => void;
  onResetInstance: (id: string) => void;
  onCreateNew: () => void;
}) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading instances...</p>
        </div>
      </div>
    );
  }

  if (instances.length === 0) {
    return (
      <EmptyState
        icon={<FolderOpen className="h-12 w-12" />}
        title="No Sandpack Instances"
        description="Create your first Sandpack instance to get started with browser-based development"
        action={
          <Button onClick={onCreateNew}>
            <Plus className="h-4 w-4 mr-2" />
            Create First Instance
          </Button>
        }
      />
    );
  }

  return (
    <div className="p-4 space-y-4">
      {instances.map((instance) => (
        <Card
          key={instance.id}
          className={`cursor-pointer transition-colors ${
            activeInstance?.id === instance.id
              ? 'border-primary bg-primary/5'
              : 'hover:border-primary/50'
          }`}
          onClick={() => onSelectInstance(instance.id)}
        >
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="font-medium">{instance.name}</h3>
                <p className="text-sm text-muted-foreground">
                  Template: {instance.config.template}
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant={instance.status === 'running' ? 'default' : 'secondary'}>
                    {instance.status}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    Created {instance.createdAt.toLocaleDateString()}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onResetInstance(instance.id);
                  }}
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteInstance(instance.id);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Empty State Component
function EmptyState({
  icon,
  title,
  description,
  action
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  action?: React.ReactNode;
}) {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="text-muted-foreground mb-4">
          {icon}
        </div>
        <h3 className="text-lg font-medium mb-2">{title}</h3>
        <p className="text-muted-foreground mb-4 max-w-md">{description}</p>
        {action}
      </div>
    </div>
  );
}
