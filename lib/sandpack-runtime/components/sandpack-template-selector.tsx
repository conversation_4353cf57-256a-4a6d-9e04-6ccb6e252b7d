/**
 * Sandpack Template Selector Component
 * 
 * Component for selecting and creating Sandpack instances from templates
 */

'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Play, 
  Star, 
  Code, 
  Globe, 
  Server, 
  TestTube,
  Filter,
  Grid,
  List
} from 'lucide-react';
import { 
  getAvailableTemplates, 
  getPopularTemplates, 
  getFrontendTemplates, 
  getBackendTemplates, 
  getFullstackTemplates,
  searchTemplates 
} from '../templates';
import { SandpackTemplate, SandpackInstanceConfig } from '../types';

interface SandpackTemplateSelectorProps {
  onCreateInstance: (config: SandpackInstanceConfig) => Promise<void>;
  projectId?: string;
  className?: string;
}

export function SandpackTemplateSelector({ 
  onCreateInstance, 
  projectId, 
  className 
}: SandpackTemplateSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<SandpackTemplate | null>(null);
  const [instanceName, setInstanceName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeCategory, setActiveCategory] = useState('all');

  const allTemplates = getAvailableTemplates();
  const popularTemplates = getPopularTemplates();
  const frontendTemplates = getFrontendTemplates();
  const backendTemplates = getBackendTemplates();
  const fullstackTemplates = getFullstackTemplates();

  const filteredTemplates = useMemo(() => {
    let templates = allTemplates;

    // Filter by category
    switch (activeCategory) {
      case 'popular':
        templates = popularTemplates;
        break;
      case 'frontend':
        templates = frontendTemplates;
        break;
      case 'backend':
        templates = backendTemplates;
        break;
      case 'fullstack':
        templates = fullstackTemplates;
        break;
      default:
        templates = allTemplates;
    }

    // Filter by search query
    if (searchQuery.trim()) {
      templates = searchTemplates(searchQuery);
    }

    return templates;
  }, [searchQuery, activeCategory, allTemplates, popularTemplates, frontendTemplates, backendTemplates, fullstackTemplates]);

  const handleCreateInstance = async () => {
    if (!selectedTemplate || !instanceName.trim()) return;

    setIsCreating(true);
    try {
      const config: SandpackInstanceConfig = {
        id: '',
        name: instanceName,
        template: selectedTemplate.template,
        files: selectedTemplate.files,
        dependencies: selectedTemplate.dependencies,
        devDependencies: selectedTemplate.devDependencies,
        projectId,
        autorun: true,
        showConsole: true,
        showFileExplorer: true
      };

      await onCreateInstance(config);
      
      // Reset form
      setSelectedTemplate(null);
      setInstanceName('');
    } catch (error) {
      console.error('Failed to create instance:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const categories = [
    { id: 'all', label: 'All Templates', icon: <Grid className="h-4 w-4" /> },
    { id: 'popular', label: 'Popular', icon: <Star className="h-4 w-4" /> },
    { id: 'frontend', label: 'Frontend', icon: <Globe className="h-4 w-4" /> },
    { id: 'backend', label: 'Backend', icon: <Server className="h-4 w-4" /> },
    { id: 'fullstack', label: 'Full Stack', icon: <Code className="h-4 w-4" /> }
  ];

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-border">
        <h2 className="text-2xl font-bold mb-2">Create New Sandpack Instance</h2>
        <p className="text-muted-foreground">
          Choose a template to get started with your development environment
        </p>
      </div>

      <div className="flex-1 flex">
        {/* Template Browser */}
        <div className="flex-1 flex flex-col">
          {/* Search and Filters */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center gap-4 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search templates..."
                  className="pl-10"
                />
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Category Tabs */}
            <Tabs value={activeCategory} onValueChange={setActiveCategory}>
              <TabsList className="grid w-full grid-cols-5">
                {categories.map((category) => (
                  <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                    {category.icon}
                    {category.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>

          {/* Template Grid/List */}
          <div className="flex-1 p-4 overflow-y-auto">
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-12">
                <Filter className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No templates found</p>
                <p className="text-sm text-muted-foreground">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className={
                viewMode === 'grid' 
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
                  : 'space-y-2'
              }>
                {filteredTemplates.map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    isSelected={selectedTemplate?.id === template.id}
                    onClick={() => setSelectedTemplate(template)}
                    viewMode={viewMode}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Instance Creation Panel */}
        <div className="w-80 border-l border-border flex flex-col">
          <div className="p-4 border-b border-border">
            <h3 className="font-semibold">Create Instance</h3>
          </div>

          <div className="flex-1 p-4">
            {selectedTemplate ? (
              <div className="space-y-4">
                {/* Selected Template Info */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <span className="text-2xl">{selectedTemplate.icon}</span>
                      {selectedTemplate.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">
                      {selectedTemplate.description}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {selectedTemplate.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Instance Configuration */}
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium">Instance Name</label>
                    <Input
                      value={instanceName}
                      onChange={(e) => setInstanceName(e.target.value)}
                      placeholder={`My ${selectedTemplate.name} Project`}
                      className="mt-1"
                    />
                  </div>

                  <Button
                    onClick={handleCreateInstance}
                    disabled={!instanceName.trim() || isCreating}
                    className="w-full"
                  >
                    {isCreating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Create Instance
                      </>
                    )}
                  </Button>
                </div>

                {/* Template Details */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Template Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <span className="text-xs font-medium">Framework:</span>
                      <span className="text-xs text-muted-foreground ml-2">
                        {selectedTemplate.framework}
                      </span>
                    </div>
                    {selectedTemplate.dependencies && Object.keys(selectedTemplate.dependencies).length > 0 && (
                      <div>
                        <span className="text-xs font-medium">Dependencies:</span>
                        <div className="mt-1 space-y-1">
                          {Object.entries(selectedTemplate.dependencies).map(([pkg, version]) => (
                            <div key={pkg} className="text-xs text-muted-foreground">
                              {pkg}@{version}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="text-center py-12">
                <Code className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Select a template</p>
                <p className="text-sm text-muted-foreground">Choose a template to create your instance</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Template Card Component
function TemplateCard({ 
  template, 
  isSelected, 
  onClick, 
  viewMode 
}: {
  template: SandpackTemplate;
  isSelected: boolean;
  onClick: () => void;
  viewMode: 'grid' | 'list';
}) {
  if (viewMode === 'list') {
    return (
      <div
        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
          isSelected
            ? 'border-primary bg-primary/5'
            : 'border-border hover:border-primary/50'
        }`}
        onClick={onClick}
      >
        <div className="flex items-center gap-4">
          <span className="text-2xl">{template.icon}</span>
          <div className="flex-1">
            <h3 className="font-medium">{template.name}</h3>
            <p className="text-sm text-muted-foreground">{template.description}</p>
          </div>
          <div className="flex flex-wrap gap-1">
            {template.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card
      className={`cursor-pointer transition-colors ${
        isSelected
          ? 'border-primary bg-primary/5'
          : 'hover:border-primary/50'
      }`}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="text-2xl">{template.icon}</span>
          {template.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-3">
          {template.description}
        </p>
        <div className="flex flex-wrap gap-1">
          {template.tags.map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
