/**
 * Sandpack AI Editor Component
 * 
 * Enhanced Sandpack editor with AI assistance capabilities
 */

'use client';

import React, { useState, useCallback } from 'react';
import {
  SandpackProvider,
  SandpackLayout,
  SandpackCodeEditor,
  SandpackPreview,
  SandpackConsole,
  SandpackFileExplorer,
  useSandpack,
  useActiveCode
} from '@codesandbox/sandpack-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  Code, 
  FileText, 
  Play, 
  Square, 
  RotateCcw,
  Wand2,
  MessageSquare,
  Lightbulb,
  Bug,
  Sparkles
} from 'lucide-react';
import { SandpackInstance } from '../types';

interface SandpackAIEditorProps {
  instance: SandpackInstance;
  onFileUpdate?: (path: string, content: string) => void;
  onAIRequest?: (prompt: string, context: any) => Promise<string>;
  className?: string;
}

export function SandpackAIEditor({ 
  instance, 
  onFileUpdate, 
  onAIRequest,
  className 
}: SandpackAIEditorProps) {
  const [aiPrompt, setAiPrompt] = useState('');
  const [isAILoading, setIsAILoading] = useState(false);
  const [aiResponse, setAiResponse] = useState('');
  const [activeTab, setActiveTab] = useState('editor');

  return (
    <div className={`h-full flex flex-col ${className}`}>
      <SandpackProvider
        template={instance.config.template}
        files={instance.files}
        customSetup={{
          dependencies: instance.config.dependencies || {},
          devDependencies: instance.config.devDependencies || {}
        }}
        options={{
          autorun: instance.config.autorun,
          autoReload: instance.config.autoReload,
          showConsole: instance.config.showConsole
        }}
      >
        <div className="flex h-full">
          {/* Main Editor Area */}
          <div className="flex-1 flex flex-col">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="editor">
                  <Code className="h-4 w-4 mr-2" />
                  Editor
                </TabsTrigger>
                <TabsTrigger value="preview">
                  <Play className="h-4 w-4 mr-2" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="console">
                  <FileText className="h-4 w-4 mr-2" />
                  Console
                </TabsTrigger>
              </TabsList>

              <TabsContent value="editor" className="flex-1">
                <SandpackLayout>
                  <SandpackFileExplorer />
                  <SandpackCodeEditor 
                    showTabs
                    showLineNumbers
                    showInlineErrors
                    wrapContent
                    closableTabs
                  />
                </SandpackLayout>
              </TabsContent>

              <TabsContent value="preview" className="flex-1">
                <SandpackPreview 
                  showNavigator
                  showRefreshButton
                  showOpenInCodeSandbox
                />
              </TabsContent>

              <TabsContent value="console" className="flex-1">
                <SandpackConsole 
                  showHeader
                  showSyntaxError
                  showResetConsoleButton
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* AI Assistant Panel */}
          <div className="w-80 border-l border-border flex flex-col">
            <SandpackAIAssistant
              instance={instance}
              onAIRequest={onAIRequest}
              onFileUpdate={onFileUpdate}
            />
          </div>
        </div>
      </SandpackProvider>
    </div>
  );
}

// AI Assistant Panel Component
function SandpackAIAssistant({ 
  instance, 
  onAIRequest, 
  onFileUpdate 
}: {
  instance: SandpackInstance;
  onAIRequest?: (prompt: string, context: any) => Promise<string>;
  onFileUpdate?: (path: string, content: string) => void;
}) {
  const { sandpack } = useSandpack();
  const { code, updateCode } = useActiveCode();
  
  const [aiPrompt, setAiPrompt] = useState('');
  const [isAILoading, setIsAILoading] = useState(false);
  const [aiResponse, setAiResponse] = useState('');
  const [aiHistory, setAiHistory] = useState<Array<{
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
  }>>([]);

  const handleAIRequest = useCallback(async (prompt: string, action: string) => {
    if (!onAIRequest || !prompt.trim()) return;

    setIsAILoading(true);
    setAiHistory(prev => [...prev, {
      type: 'user',
      content: `${action}: ${prompt}`,
      timestamp: new Date()
    }]);

    try {
      const context = {
        activeFile: sandpack.activeFile,
        files: sandpack.files,
        template: instance.config.template,
        code: code,
        action: action
      };

      const response = await onAIRequest(prompt, context);
      
      setAiResponse(response);
      setAiHistory(prev => [...prev, {
        type: 'ai',
        content: response,
        timestamp: new Date()
      }]);

      // If the response contains code, offer to apply it
      if (action === 'generate' || action === 'fix') {
        // Extract code from response (simple implementation)
        const codeMatch = response.match(/```[\s\S]*?```/);
        if (codeMatch) {
          const extractedCode = codeMatch[0].replace(/```\w*\n?/g, '').replace(/```/g, '');
          if (extractedCode.trim()) {
            updateCode(extractedCode);
            if (onFileUpdate && sandpack.activeFile) {
              onFileUpdate(sandpack.activeFile, extractedCode);
            }
          }
        }
      }
    } catch (error) {
      console.error('AI request failed:', error);
      setAiHistory(prev => [...prev, {
        type: 'ai',
        content: 'Sorry, I encountered an error processing your request.',
        timestamp: new Date()
      }]);
    } finally {
      setIsAILoading(false);
      setAiPrompt('');
    }
  }, [onAIRequest, sandpack, code, updateCode, onFileUpdate, instance.config.template]);

  const quickActions = [
    {
      label: 'Explain Code',
      icon: <Lightbulb className="h-4 w-4" />,
      action: 'explain',
      prompt: 'Explain what this code does'
    },
    {
      label: 'Fix Bugs',
      icon: <Bug className="h-4 w-4" />,
      action: 'fix',
      prompt: 'Find and fix any bugs in this code'
    },
    {
      label: 'Optimize',
      icon: <Sparkles className="h-4 w-4" />,
      action: 'optimize',
      prompt: 'Optimize this code for better performance'
    },
    {
      label: 'Add Comments',
      icon: <MessageSquare className="h-4 w-4" />,
      action: 'comment',
      prompt: 'Add helpful comments to this code'
    }
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-border">
        <h3 className="font-semibold flex items-center gap-2">
          <Bot className="h-5 w-5" />
          AI Assistant
        </h3>
        <Badge variant="outline" className="mt-2">
          {instance.config.template}
        </Badge>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-border">
        <h4 className="text-sm font-medium mb-2">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map((action) => (
            <Button
              key={action.action}
              variant="outline"
              size="sm"
              onClick={() => handleAIRequest(action.prompt, action.action)}
              disabled={isAILoading}
              className="flex items-center gap-1 text-xs"
            >
              {action.icon}
              {action.label}
            </Button>
          ))}
        </div>
      </div>

      {/* AI Chat History */}
      <div className="flex-1 p-4 overflow-y-auto">
        <h4 className="text-sm font-medium mb-2">Chat History</h4>
        <div className="space-y-3">
          {aiHistory.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              Ask me anything about your code!
            </p>
          ) : (
            aiHistory.map((message, index) => (
              <div
                key={index}
                className={`p-2 rounded-lg text-sm ${
                  message.type === 'user'
                    ? 'bg-primary/10 border border-primary/20'
                    : 'bg-muted border border-border'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  {message.type === 'user' ? (
                    <MessageSquare className="h-3 w-3" />
                  ) : (
                    <Bot className="h-3 w-3" />
                  )}
                  <span className="text-xs font-medium">
                    {message.type === 'user' ? 'You' : 'AI'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <p className="whitespace-pre-wrap">{message.content}</p>
              </div>
            ))
          )}
          {isAILoading && (
            <div className="p-2 rounded-lg bg-muted border border-border">
              <div className="flex items-center gap-2">
                <Bot className="h-3 w-3" />
                <span className="text-xs font-medium">AI</span>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary"></div>
              </div>
              <p className="text-sm mt-1">Thinking...</p>
            </div>
          )}
        </div>
      </div>

      {/* AI Input */}
      <div className="p-4 border-t border-border">
        <div className="space-y-2">
          <Textarea
            value={aiPrompt}
            onChange={(e) => setAiPrompt(e.target.value)}
            placeholder="Ask AI to help with your code..."
            className="min-h-[60px] resize-none"
            disabled={isAILoading}
          />
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => handleAIRequest(aiPrompt, 'chat')}
              disabled={!aiPrompt.trim() || isAILoading}
              className="flex-1"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              Ask AI
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleAIRequest(aiPrompt, 'generate')}
              disabled={!aiPrompt.trim() || isAILoading}
            >
              <Code className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
