/**
 * WebContainer AI Tools
 * 
 * AI tool integrations for WebContainer operations
 * Provides AI agents with WebContainer management capabilities
 */

import { tool } from 'ai';
import { z } from 'zod';
import { WebContainerClient } from '@/lib/webcontainer-runtime/api/webcontainer-client';
import { 
  WebContainerConfig, 
  WebContainerTemplate,
  ProcessOptions 
} from '@/lib/webcontainer-runtime/types';
import { getDefaultTemplates } from '@/lib/webcontainer-runtime/utils/webcontainer-helpers';

// Initialize WebContainer client
const webContainerClient = new WebContainerClient('/api/webcontainer-api');

/**
 * Create a new WebContainer instance
 */
export const createWebContainer = tool({
  description: 'Create a new WebContainer instance for browser-based Node.js development',
  parameters: z.object({
    name: z.string().describe('Name for the WebContainer instance'),
    template: z.string().optional().describe('Template ID to use (react, vue, node, etc.)'),
    projectId: z.string().optional().describe('Project ID to associate with the container'),
    autoStart: z.boolean().default(false).describe('Whether to automatically start the container'),
    environment: z.record(z.string()).optional().describe('Environment variables to set'),
  }),
  execute: async ({ name, template, projectId, autoStart, environment }) => {
    try {
      // Get template if specified
      let templateData: WebContainerTemplate | undefined;
      if (template) {
        const templates = getDefaultTemplates();
        templateData = templates.find(t => t.id === template || t.name.toLowerCase().includes(template.toLowerCase()));
        
        if (!templateData) {
          return {
            success: false,
            error: `Template '${template}' not found. Available templates: ${templates.map(t => t.id).join(', ')}`
          };
        }
      }

      // Create container configuration
      const config: WebContainerConfig = {
        name,
        projectId,
        autoStart,
        environment: environment || {},
        workingDirectory: '/',
        persistent: true,
        resources: {
          memory: 512,
          cpu: 1
        }
      };

      // Create container
      const containerId = await webContainerClient.createContainer(
        config, 
        templateData?.files
      );

      // Start container if requested
      if (autoStart) {
        await webContainerClient.startContainer(containerId);
      }

      return {
        success: true,
        containerId,
        name,
        template: templateData?.name,
        message: `WebContainer '${name}' created successfully${autoStart ? ' and started' : ''}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create WebContainer'
      };
    }
  },
});

/**
 * List all WebContainer instances
 */
export const listWebContainers = tool({
  description: 'List all WebContainer instances and their status',
  parameters: z.object({
    projectId: z.string().optional().describe('Filter by project ID'),
  }),
  execute: async ({ projectId }) => {
    try {
      const containers = await webContainerClient.listContainers();
      
      // Filter by project ID if specified
      const filteredContainers = projectId 
        ? containers.filter(c => c.config.projectId === projectId)
        : containers;

      return {
        success: true,
        containers: filteredContainers.map(container => ({
          id: container.id,
          name: container.name,
          status: container.status,
          projectId: container.config.projectId,
          createdAt: container.createdAt,
          lastActivity: container.lastActivity
        })),
        total: filteredContainers.length
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list WebContainers'
      };
    }
  },
});

/**
 * Execute command in WebContainer
 */
export const executeWebContainerCommand = tool({
  description: 'Execute a command in a WebContainer instance',
  parameters: z.object({
    containerId: z.string().describe('WebContainer instance ID'),
    command: z.string().describe('Command to execute'),
    args: z.array(z.string()).optional().describe('Command arguments'),
    cwd: z.string().optional().describe('Working directory for the command'),
    timeout: z.number().optional().describe('Command timeout in milliseconds'),
  }),
  execute: async ({ containerId, command, args = [], cwd, timeout }) => {
    try {
      const options: ProcessOptions = {};
      if (cwd) options.cwd = cwd;
      if (timeout) options.timeout = timeout;

      const processInfo = await webContainerClient.executeCommand(
        containerId,
        command,
        args,
        options
      );

      return {
        success: true,
        processId: processInfo.id,
        command: `${command} ${args.join(' ')}`.trim(),
        status: processInfo.status,
        startTime: processInfo.startTime,
        cwd: processInfo.cwd
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to execute command'
      };
    }
  },
});

/**
 * Read file from WebContainer
 */
export const readWebContainerFile = tool({
  description: 'Read a file from a WebContainer instance',
  parameters: z.object({
    containerId: z.string().describe('WebContainer instance ID'),
    path: z.string().describe('File path to read'),
  }),
  execute: async ({ containerId, path }) => {
    try {
      const content = await webContainerClient.readFile(containerId, path);
      
      return {
        success: true,
        path,
        content,
        size: content.length
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to read file'
      };
    }
  },
});

/**
 * Write file to WebContainer
 */
export const writeWebContainerFile = tool({
  description: 'Write a file to a WebContainer instance',
  parameters: z.object({
    containerId: z.string().describe('WebContainer instance ID'),
    path: z.string().describe('File path to write'),
    content: z.string().describe('File content to write'),
  }),
  execute: async ({ containerId, path, content }) => {
    try {
      await webContainerClient.writeFile(containerId, path, content);
      
      return {
        success: true,
        path,
        size: content.length,
        message: `File '${path}' written successfully`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to write file'
      };
    }
  },
});

/**
 * List files in WebContainer
 */
export const listWebContainerFiles = tool({
  description: 'List files and directories in a WebContainer instance',
  parameters: z.object({
    containerId: z.string().describe('WebContainer instance ID'),
    path: z.string().default('/').describe('Directory path to list'),
    maxDepth: z.number().default(3).describe('Maximum directory depth to traverse'),
  }),
  execute: async ({ containerId, path, maxDepth }) => {
    try {
      const files = await webContainerClient.listFiles(containerId, path);
      
      return {
        success: true,
        path,
        files: files.map(file => ({
          name: file.name,
          path: file.path,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified
        })),
        total: files.length
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list files'
      };
    }
  },
});

/**
 * Start development server in WebContainer
 */
export const startWebContainerDevServer = tool({
  description: 'Start a development server in a WebContainer instance',
  parameters: z.object({
    containerId: z.string().describe('WebContainer instance ID'),
    command: z.string().default('npm run dev').describe('Development server command'),
    port: z.number().optional().describe('Port to run the server on'),
  }),
  execute: async ({ containerId, command, port }) => {
    try {
      const processInfo = await webContainerClient.startDevServer(containerId, command);
      
      return {
        success: true,
        processId: processInfo.id,
        command,
        port,
        status: processInfo.status,
        message: `Development server started with command: ${command}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start development server'
      };
    }
  },
});

/**
 * Install packages in WebContainer
 */
export const installWebContainerPackages = tool({
  description: 'Install npm packages in a WebContainer instance',
  parameters: z.object({
    containerId: z.string().describe('WebContainer instance ID'),
    packages: z.array(z.string()).describe('Package names to install'),
    packageManager: z.enum(['npm', 'yarn', 'pnpm']).default('npm').describe('Package manager to use'),
    dev: z.boolean().default(false).describe('Install as development dependencies'),
  }),
  execute: async ({ containerId, packages, packageManager, dev }) => {
    try {
      const processInfo = await webContainerClient.installPackages(
        containerId, 
        packages, 
        { packageManager, dev }
      );
      
      return {
        success: true,
        processId: processInfo.id,
        packages,
        packageManager,
        dev,
        message: `Installing packages: ${packages.join(', ')} using ${packageManager}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to install packages'
      };
    }
  },
});

/**
 * Get available WebContainer templates
 */
export const getWebContainerTemplates = tool({
  description: 'Get list of available WebContainer project templates',
  parameters: z.object({}),
  execute: async () => {
    try {
      const templates = getDefaultTemplates();
      
      return {
        success: true,
        templates: templates.map(template => ({
          id: template.id,
          name: template.name,
          description: template.description,
          tags: template.tags,
          framework: template.config.framework
        })),
        total: templates.length
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get templates'
      };
    }
  },
});

// Export all WebContainer AI tools
export const webContainerAITools = {
  createWebContainer,
  listWebContainers,
  executeWebContainerCommand,
  readWebContainerFile,
  writeWebContainerFile,
  listWebContainerFiles,
  startWebContainerDevServer,
  installWebContainerPackages,
  getWebContainerTemplates,
};
