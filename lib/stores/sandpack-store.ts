/**
 * Sandpack Store
 * 
 * Zustand store for managing Sandpack instances and state
 * Provides a centralized state management solution for Sandpack operations
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { useEffect } from 'react';
import { sandpackManager } from '@/lib/sandpack-runtime/core/sandpack-manager';
import { 
  SandpackInstance, 
  SandpackInstanceConfig,
  SandpackTemplate,
  SandpackError,
  SandpackConsoleOutput,
  SandpackTestResult,
  SandpackSettings,
  DEFAULT_SANDPACK_SETTINGS,
  SandpackIntegrationHook
} from '@/lib/sandpack-runtime/types';
import { getAvailableTemplates } from '@/lib/sandpack-runtime/templates';

// Store state interface
interface SandpackState {
  // Core state
  instances: SandpackInstance[];
  activeInstanceId: string | null;
  isLoading: boolean;
  error: SandpackError | null;
  
  // Runtime management
  isManagerInitialized: boolean;
  
  // Templates
  templates: SandpackTemplate[];
  
  // Settings
  settings: SandpackSettings;
  
  // Console output
  consoleOutput: Record<string, SandpackConsoleOutput[]>;
  
  // Test results
  testResults: Record<string, SandpackTestResult[]>;
  
  // File operations
  activeFile: {
    instanceId: string;
    path: string;
    content: string;
  } | null;
}

// Store actions interface
interface SandpackActions {
  // Runtime management
  initializeManager: () => Promise<void>;
  
  // Instance management
  createInstance: (config: SandpackInstanceConfig) => Promise<string>;
  updateInstance: (id: string, updates: Partial<SandpackInstanceConfig>) => Promise<void>;
  deleteInstance: (id: string) => Promise<void>;
  resetInstance: (id: string) => Promise<void>;
  setActiveInstance: (id: string | null) => void;
  getInstance: (id: string) => SandpackInstance | null;
  listInstances: () => SandpackInstance[];
  
  // File operations
  updateFile: (instanceId: string, path: string, content: string) => Promise<void>;
  createFile: (instanceId: string, path: string, content?: string) => Promise<void>;
  deleteFile: (instanceId: string, path: string) => Promise<void>;
  setActiveFile: (instanceId: string, path: string) => void;
  
  // Package management
  installPackages: (instanceId: string, packages: string[], dev?: boolean) => Promise<void>;
  
  // Console operations
  addConsoleOutput: (output: SandpackConsoleOutput) => void;
  clearConsole: (instanceId: string) => void;
  
  // Test operations
  runTests: (instanceId: string) => Promise<SandpackTestResult[]>;
  addTestResult: (result: SandpackTestResult) => void;
  clearTestResults: (instanceId: string) => void;
  
  // Settings
  updateSettings: (updates: Partial<SandpackSettings>) => void;
  resetSettings: () => void;
  
  // Templates
  loadTemplates: () => void;
  createFromTemplate: (templateId: string, config: Partial<SandpackInstanceConfig>) => Promise<string>;
  
  // Cleanup
  cleanup: () => void;
}

// Combined store type
type SandpackStore = SandpackState & SandpackActions;

// Create the store
export const useSandpackStore = create<SandpackStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    instances: [],
    activeInstanceId: null,
    isLoading: false,
    error: null,
    
    isManagerInitialized: false,
    
    templates: [],
    settings: DEFAULT_SANDPACK_SETTINGS,
    consoleOutput: {},
    testResults: {},
    activeFile: null,
    
    // Initialize manager
    initializeManager: async () => {
      const state = get();
      if (state.isManagerInitialized) return;

      set({ isLoading: true, error: null });

      try {
        await sandpackManager.initialize();
        
        set({
          isManagerInitialized: true,
          isLoading: false
        });
      } catch (error) {
        set({
          error: error as SandpackError,
          isLoading: false
        });
        throw error;
      }
    },
    
    // Instance management
    createInstance: async (config) => {
      const state = get();
      if (!state.isManagerInitialized) {
        await get().initializeManager();
      }

      set({ isLoading: true, error: null });

      try {
        const instanceId = await sandpackManager.createInstance(config);
        
        // Refresh instance list
        const instances = sandpackManager.listInstances();
        
        set({
          instances,
          activeInstanceId: instanceId,
          isLoading: false
        });

        return instanceId;
      } catch (error) {
        set({
          error: error as SandpackError,
          isLoading: false
        });
        throw error;
      }
    },
    
    updateInstance: async (id, updates) => {
      try {
        await sandpackManager.updateInstance(id, updates);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    deleteInstance: async (id) => {
      try {
        await sandpackManager.deleteInstance(id);
        
        const instances = sandpackManager.listInstances();
        const state = get();
        
        set({
          instances,
          activeInstanceId: state.activeInstanceId === id ? null : state.activeInstanceId,
          activeFile: state.activeFile?.instanceId === id ? null : state.activeFile
        });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    resetInstance: async (id) => {
      try {
        await sandpackManager.resetInstance(id);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    setActiveInstance: (id) => {
      set({ activeInstanceId: id });
    },
    
    getInstance: (id) => {
      return sandpackManager.getInstance(id);
    },
    
    listInstances: () => {
      return sandpackManager.listInstances();
    },
    
    // File operations
    updateFile: async (instanceId, path, content) => {
      try {
        await sandpackManager.updateFile(instanceId, path, content);
        
        const state = get();
        if (state.activeFile?.instanceId === instanceId && state.activeFile?.path === path) {
          set({
            activeFile: { ...state.activeFile, content }
          });
        }
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    createFile: async (instanceId, path, content = '') => {
      try {
        await sandpackManager.createFile(instanceId, path, content);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    deleteFile: async (instanceId, path) => {
      try {
        await sandpackManager.deleteFile(instanceId, path);
        
        const state = get();
        if (state.activeFile?.instanceId === instanceId && state.activeFile?.path === path) {
          set({ activeFile: null });
        }
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    setActiveFile: (instanceId, path) => {
      try {
        sandpackManager.setActiveFile(instanceId, path);
        
        const instance = sandpackManager.getInstance(instanceId);
        if (instance && instance.files[path]) {
          set({
            activeFile: {
              instanceId,
              path,
              content: instance.files[path].code || ''
            }
          });
        }
      } catch (error) {
        set({ error: error as SandpackError });
      }
    },
    
    // Package management
    installPackages: async (instanceId, packages, dev = false) => {
      try {
        await sandpackManager.installPackages(instanceId, packages, dev);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    // Console operations
    addConsoleOutput: (output) => {
      const state = get();
      const instanceOutput = state.consoleOutput[output.instanceId] || [];
      
      set({
        consoleOutput: {
          ...state.consoleOutput,
          [output.instanceId]: [...instanceOutput, output]
        }
      });
    },
    
    clearConsole: (instanceId) => {
      const state = get();
      set({
        consoleOutput: {
          ...state.consoleOutput,
          [instanceId]: []
        }
      });
    },
    
    // Test operations
    runTests: async (instanceId) => {
      // This would integrate with Sandpack's test runner
      // For now, return empty array
      return [];
    },
    
    addTestResult: (result) => {
      const state = get();
      const instanceResults = state.testResults[result.instanceId] || [];
      
      set({
        testResults: {
          ...state.testResults,
          [result.instanceId]: [...instanceResults, result]
        }
      });
    },
    
    clearTestResults: (instanceId) => {
      const state = get();
      set({
        testResults: {
          ...state.testResults,
          [instanceId]: []
        }
      });
    },
    
    // Settings
    updateSettings: (updates) => {
      const state = get();
      set({
        settings: { ...state.settings, ...updates }
      });
    },
    
    resetSettings: () => {
      set({ settings: DEFAULT_SANDPACK_SETTINGS });
    },
    
    // Templates
    loadTemplates: () => {
      const templates = getAvailableTemplates();
      set({ templates });
    },
    
    createFromTemplate: async (templateId, config) => {
      const state = get();
      const template = state.templates.find(t => t.id === templateId);
      
      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }
      
      const instanceConfig: SandpackInstanceConfig = {
        id: '',
        name: config.name || template.name,
        template: template.template,
        files: template.files,
        dependencies: template.dependencies,
        devDependencies: template.devDependencies,
        ...config
      };
      
      return await get().createInstance(instanceConfig);
    },
    
    // Cleanup
    cleanup: () => {
      set({
        instances: [],
        activeInstanceId: null,
        error: null,
        isLoading: false,
        isManagerInitialized: false,
        consoleOutput: {},
        testResults: {},
        activeFile: null
      });
      
      sandpackManager.cleanup();
    }
  }))
);

// Integration hook for workspace
export function useSandpackIntegration(projectId?: string): SandpackIntegrationHook {
  const {
    instances,
    activeInstanceId,
    isLoading,
    error,
    initializeManager,
    createInstance,
    updateInstance,
    deleteInstance,
    setActiveInstance,
    updateFile,
    deleteFile,
    installPackages,
    runTests,
    clearConsole,
    resetInstance,
    loadTemplates
  } = useSandpackStore();

  // Initialize manager on mount
  useEffect(() => {
    initializeManager();
    loadTemplates();
  }, [initializeManager, loadTemplates]);

  const activeInstance = activeInstanceId ? instances.find(i => i.id === activeInstanceId) || null : null;

  return {
    instances,
    activeInstance,
    isLoading,
    error,
    createInstance,
    updateInstance,
    deleteInstance,
    setActiveInstance,
    updateFile,
    deleteFile,
    installPackages,
    runTests,
    clearConsole,
    resetInstance
  };
}
