/**
 * Enhanced Sandpack Store
 *
 * Zustand store for managing Sandpack instances, files, and state
 * Provides comprehensive state management for Sandpack operations
 * Integrates with Sandpack React components for seamless development experience
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { useEffect, useCallback } from 'react';
import {
  SandpackFiles
} from '@codesandbox/sandpack-react';
import { sandpackManager } from '@/lib/sandpack-runtime/core/sandpack-manager';
import {
  SandpackInstance,
  SandpackInstanceConfig,
  SandpackTemplate,
  SandpackError,
  SandpackConsoleOutput,
  SandpackTestResult,
  SandpackSettings,
  DEFAULT_SANDPACK_SETTINGS,
  SandpackIntegrationHook
} from '@/lib/sandpack-runtime/types';
import { getAvailableTemplates } from '@/lib/sandpack-runtime/templates';

// Sandpack Runtime State for each instance
interface SandpackRuntimeState {
  status: 'idle' | 'initializing' | 'running' | 'error' | 'stopped';
  bundlerState: 'initializing' | 'bundling' | 'running' | 'error' | 'idle';
  errors: Array<{ message: string; line?: number; column?: number; path?: string }>;
  logs: Array<{ level: 'info' | 'warn' | 'error'; message: string; timestamp: Date }>;
  isReady: boolean;
  lastActivity: Date;
  previewUrl?: string;
}

// Enhanced Store state interface
interface SandpackState {
  // Core state
  instances: SandpackInstance[];
  activeInstanceId: string | null;
  isLoading: boolean;
  error: SandpackError | null;

  // Runtime management
  isManagerInitialized: boolean;

  // Runtime management - Map of instanceId to runtime state
  runtimeStates: Record<string, SandpackRuntimeState>;

  // Templates
  templates: SandpackTemplate[];

  // Settings
  settings: SandpackSettings;

  // Console output
  consoleOutput: Record<string, SandpackConsoleOutput[]>;

  // Test results
  testResults: Record<string, SandpackTestResult[]>;

  // File operations
  activeFile: {
    instanceId: string;
    path: string;
    content: string;
  } | null;

  // File system state per instance
  fileSystems: Record<string, SandpackFiles>;

  // Preview URLs
  previewUrls: Record<string, string>;

  // Hot reload state
  hotReloadEnabled: Record<string, boolean>;
}

// Enhanced Store actions interface
interface SandpackActions {
  // Runtime management
  initializeManager: () => Promise<void>;

  // Instance management
  createInstance: (config: SandpackInstanceConfig) => Promise<string>;
  updateInstance: (id: string, updates: Partial<SandpackInstanceConfig>) => Promise<void>;
  deleteInstance: (id: string) => Promise<void>;
  resetInstance: (id: string) => Promise<void>;
  setActiveInstance: (id: string | null) => void;
  getInstance: (id: string) => SandpackInstance | null;
  listInstances: () => SandpackInstance[];

  // Runtime state management
  updateRuntimeState: (instanceId: string, updates: Partial<SandpackRuntimeState>) => void;
  getRuntimeState: (instanceId: string) => SandpackRuntimeState | null;
  resetRuntimeState: (instanceId: string) => void;

  // File operations
  updateFile: (instanceId: string, path: string, content: string) => Promise<void>;
  createFile: (instanceId: string, path: string, content?: string) => Promise<void>;
  deleteFile: (instanceId: string, path: string) => Promise<void>;
  setActiveFile: (instanceId: string, path: string) => void;
  syncFiles: (instanceId: string, files: SandpackFiles) => Promise<void>;
  getFiles: (instanceId: string) => SandpackFiles;

  // Package management
  installPackages: (instanceId: string, packages: string[], dev?: boolean) => Promise<void>;

  // Console operations
  addConsoleOutput: (output: SandpackConsoleOutput) => void;
  clearConsole: (instanceId: string) => void;

  // Test operations
  runTests: (instanceId: string) => Promise<SandpackTestResult[]>;
  addTestResult: (result: SandpackTestResult) => void;
  clearTestResults: (instanceId: string) => void;

  // Preview operations
  getPreviewUrl: (instanceId: string) => string | null;
  updatePreviewUrl: (instanceId: string, url: string) => void;

  // Hot reload
  enableHotReload: (instanceId: string) => void;
  disableHotReload: (instanceId: string) => void;

  // Settings
  updateSettings: (updates: Partial<SandpackSettings>) => void;
  resetSettings: () => void;

  // Templates
  loadTemplates: () => void;
  createFromTemplate: (templateId: string, config: Partial<SandpackInstanceConfig>) => Promise<string>;

  // Cleanup
  cleanup: () => void;
}

// Combined store type
type SandpackStore = SandpackState & SandpackActions;

// Create the store
export const useSandpackStore = create<SandpackStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    instances: [],
    activeInstanceId: null,
    isLoading: false,
    error: null,

    isManagerInitialized: false,

    // Runtime states
    runtimeStates: {},

    templates: [],
    settings: DEFAULT_SANDPACK_SETTINGS,
    consoleOutput: {},
    testResults: {},
    activeFile: null,

    // File system state
    fileSystems: {},

    // Preview URLs
    previewUrls: {},

    // Hot reload state
    hotReloadEnabled: {},
    
    // Initialize manager
    initializeManager: async () => {
      const state = get();
      if (state.isManagerInitialized) return;

      set({ isLoading: true, error: null });

      try {
        await sandpackManager.initialize();
        
        set({
          isManagerInitialized: true,
          isLoading: false
        });
      } catch (error) {
        set({
          error: error as SandpackError,
          isLoading: false
        });
        throw error;
      }
    },
    
    // Instance management
    createInstance: async (config) => {
      const state = get();
      if (!state.isManagerInitialized) {
        await get().initializeManager();
      }

      set({ isLoading: true, error: null });

      try {
        const instanceId = await sandpackManager.createInstance(config);
        
        // Refresh instance list
        const instances = sandpackManager.listInstances();
        
        set({
          instances,
          activeInstanceId: instanceId,
          isLoading: false
        });

        return instanceId;
      } catch (error) {
        set({
          error: error as SandpackError,
          isLoading: false
        });
        throw error;
      }
    },
    
    updateInstance: async (id, updates) => {
      try {
        await sandpackManager.updateInstance(id, updates);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    deleteInstance: async (id) => {
      try {
        await sandpackManager.deleteInstance(id);
        
        const instances = sandpackManager.listInstances();
        const state = get();
        
        set({
          instances,
          activeInstanceId: state.activeInstanceId === id ? null : state.activeInstanceId,
          activeFile: state.activeFile?.instanceId === id ? null : state.activeFile
        });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    resetInstance: async (id) => {
      try {
        await sandpackManager.resetInstance(id);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    setActiveInstance: (id) => {
      set({ activeInstanceId: id });
    },
    
    getInstance: (id) => {
      return sandpackManager.getInstance(id);
    },
    
    listInstances: () => {
      return sandpackManager.listInstances();
    },

    // Runtime state management
    updateRuntimeState: (instanceId, updates) => {
      const state = get();
      const currentState = state.runtimeStates[instanceId] || {
        status: 'idle',
        bundlerState: 'idle',
        errors: [],
        logs: [],
        isReady: false,
        lastActivity: new Date()
      };

      set({
        runtimeStates: {
          ...state.runtimeStates,
          [instanceId]: {
            ...currentState,
            ...updates,
            lastActivity: new Date()
          }
        }
      });
    },

    getRuntimeState: (instanceId) => {
      return get().runtimeStates[instanceId] || null;
    },

    resetRuntimeState: (instanceId) => {
      const state = get();
      const newRuntimeStates = { ...state.runtimeStates };
      delete newRuntimeStates[instanceId];

      set({ runtimeStates: newRuntimeStates });
    },

    // File operations
    updateFile: async (instanceId, path, content) => {
      try {
        await sandpackManager.updateFile(instanceId, path, content);
        
        const state = get();
        if (state.activeFile?.instanceId === instanceId && state.activeFile?.path === path) {
          set({
            activeFile: { ...state.activeFile, content }
          });
        }
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    createFile: async (instanceId, path, content = '') => {
      try {
        await sandpackManager.createFile(instanceId, path, content);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    deleteFile: async (instanceId, path) => {
      try {
        await sandpackManager.deleteFile(instanceId, path);
        
        const state = get();
        if (state.activeFile?.instanceId === instanceId && state.activeFile?.path === path) {
          set({ activeFile: null });
        }
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    setActiveFile: (instanceId, path) => {
      try {
        sandpackManager.setActiveFile(instanceId, path);

        const instance = sandpackManager.getInstance(instanceId);
        if (instance && instance.files[path]) {
          const fileContent = typeof instance.files[path] === 'string'
            ? instance.files[path] as string
            : (instance.files[path] as any)?.code || '';

          set({
            activeFile: {
              instanceId,
              path,
              content: fileContent
            }
          });
        }
      } catch (error) {
        set({ error: error as SandpackError });
      }
    },

    syncFiles: async (instanceId, files) => {
      try {
        // Update local file system state
        set({
          fileSystems: {
            ...get().fileSystems,
            [instanceId]: files
          }
        });

        // Update instance files through manager
        const instance = sandpackManager.getInstance(instanceId);
        if (instance) {
          instance.files = files;
          instance.lastActivity = new Date();
        }

        // Update runtime state to indicate files changed
        get().updateRuntimeState(instanceId, {
          lastActivity: new Date()
        });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },

    getFiles: (instanceId) => {
      return get().fileSystems[instanceId] || {};
    },
    
    // Package management
    installPackages: async (instanceId, packages, dev = false) => {
      try {
        await sandpackManager.installPackages(instanceId, packages, dev);
        
        const instances = sandpackManager.listInstances();
        set({ instances });
      } catch (error) {
        set({ error: error as SandpackError });
        throw error;
      }
    },
    
    // Console operations
    addConsoleOutput: (output) => {
      const state = get();
      const instanceOutput = state.consoleOutput[output.instanceId] || [];
      
      set({
        consoleOutput: {
          ...state.consoleOutput,
          [output.instanceId]: [...instanceOutput, output]
        }
      });
    },
    
    clearConsole: (instanceId) => {
      const state = get();
      set({
        consoleOutput: {
          ...state.consoleOutput,
          [instanceId]: []
        }
      });
    },
    
    // Test operations
    runTests: async (instanceId: string) => {
      // This would integrate with Sandpack's test runner
      // For now, return empty array
      console.log(`Running tests for instance: ${instanceId}`);
      return [];
    },
    
    addTestResult: (result) => {
      const state = get();
      const instanceResults = state.testResults[result.instanceId] || [];
      
      set({
        testResults: {
          ...state.testResults,
          [result.instanceId]: [...instanceResults, result]
        }
      });
    },
    
    clearTestResults: (instanceId) => {
      const state = get();
      set({
        testResults: {
          ...state.testResults,
          [instanceId]: []
        }
      });
    },

    // Preview operations
    getPreviewUrl: (instanceId) => {
      return get().previewUrls[instanceId] || null;
    },

    updatePreviewUrl: (instanceId, url) => {
      set({
        previewUrls: {
          ...get().previewUrls,
          [instanceId]: url
        }
      });
    },

    // Hot reload
    enableHotReload: (instanceId) => {
      set({
        hotReloadEnabled: {
          ...get().hotReloadEnabled,
          [instanceId]: true
        }
      });
    },

    disableHotReload: (instanceId) => {
      set({
        hotReloadEnabled: {
          ...get().hotReloadEnabled,
          [instanceId]: false
        }
      });
    },
    
    // Settings
    updateSettings: (updates) => {
      const state = get();
      set({
        settings: { ...state.settings, ...updates }
      });
    },
    
    resetSettings: () => {
      set({ settings: DEFAULT_SANDPACK_SETTINGS });
    },
    
    // Templates
    loadTemplates: () => {
      const templates = getAvailableTemplates();
      set({ templates });
    },
    
    createFromTemplate: async (templateId, config) => {
      const state = get();
      const template = state.templates.find(t => t.id === templateId);

      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }

      const instanceConfig: SandpackInstanceConfig = {
        id: '',
        name: config.name || template.name,
        template: template.template,
        files: template.files,
        dependencies: template.dependencies,
        devDependencies: template.devDependencies,
        ...config
      };

      return await get().createInstance(instanceConfig);
    },

    // Cleanup
    cleanup: () => {
      set({
        instances: [],
        activeInstanceId: null,
        error: null,
        isLoading: false,
        isManagerInitialized: false,
        runtimeStates: {},
        consoleOutput: {},
        testResults: {},
        activeFile: null,
        fileSystems: {},
        previewUrls: {},
        hotReloadEnabled: {}
      });

      sandpackManager.cleanup();
    }
  }))
);

// Enhanced Integration hook for workspace with runtime management
export function useSandpackIntegration(_projectId?: string): SandpackIntegrationHook & {
  // Extended runtime management
  updateRuntimeState: (instanceId: string, updates: Partial<SandpackRuntimeState>) => void;
  getRuntimeState: (instanceId: string) => SandpackRuntimeState | null;
  resetRuntimeState: (instanceId: string) => void;
  syncFiles: (instanceId: string, files: SandpackFiles) => Promise<void>;
  getFiles: (instanceId: string) => SandpackFiles;
  getPreviewUrl: (instanceId: string) => string | null;
  updatePreviewUrl: (instanceId: string, url: string) => void;
  enableHotReload: (instanceId: string) => void;
  disableHotReload: (instanceId: string) => void;
} {
  const {
    instances,
    activeInstanceId,
    isLoading,
    error,
    initializeManager,
    createInstance,
    updateInstance,
    deleteInstance,
    setActiveInstance,
    updateFile,
    deleteFile,
    installPackages,
    runTests,
    clearConsole,
    resetInstance,
    loadTemplates,
    // Runtime management
    updateRuntimeState,
    getRuntimeState,
    resetRuntimeState,
    syncFiles,
    getFiles,
    getPreviewUrl,
    updatePreviewUrl,
    enableHotReload,
    disableHotReload
  } = useSandpackStore();

  // Initialize manager on mount
  useEffect(() => {
    initializeManager();
    loadTemplates();
  }, [initializeManager, loadTemplates]);

  const activeInstance = activeInstanceId ? instances.find(i => i.id === activeInstanceId) || null : null;

  return {
    instances,
    activeInstance,
    isLoading,
    error,
    createInstance,
    updateInstance,
    deleteInstance,
    setActiveInstance,
    updateFile,
    deleteFile,
    installPackages,
    runTests,
    clearConsole,
    resetInstance,
    // Extended runtime management
    updateRuntimeState,
    getRuntimeState,
    resetRuntimeState,
    syncFiles,
    getFiles,
    getPreviewUrl,
    updatePreviewUrl,
    enableHotReload,
    disableHotReload
  };
}

// Specialized hook for Sandpack runtime management
export function useSandpackRuntime(instanceId: string | null) {
  const {
    getRuntimeState,
    updateRuntimeState,
    resetRuntimeState,
    syncFiles,
    getFiles,
    getPreviewUrl,
    updatePreviewUrl,
    enableHotReload,
    disableHotReload
  } = useSandpackIntegration();

  const runtimeState = instanceId ? getRuntimeState(instanceId) : null;

  const updateFiles = useCallback(async (files: SandpackFiles) => {
    if (instanceId) {
      await syncFiles(instanceId, files);
    }
  }, [instanceId, syncFiles]);

  const updateState = useCallback((updates: Partial<SandpackRuntimeState>) => {
    if (instanceId) {
      updateRuntimeState(instanceId, updates);
    }
  }, [instanceId, updateRuntimeState]);

  const resetState = useCallback(() => {
    if (instanceId) {
      resetRuntimeState(instanceId);
    }
  }, [instanceId, resetRuntimeState]);

  const setPreviewUrl = useCallback((url: string) => {
    if (instanceId) {
      updatePreviewUrl(instanceId, url);
    }
  }, [instanceId, updatePreviewUrl]);

  const files = instanceId ? getFiles(instanceId) : {};
  const previewUrl = instanceId ? getPreviewUrl(instanceId) : null;

  return {
    runtimeState,
    files,
    previewUrl,
    updateFiles,
    updateState,
    resetState,
    setPreviewUrl,
    enableHotReload: () => instanceId && enableHotReload(instanceId),
    disableHotReload: () => instanceId && disableHotReload(instanceId),
    isReady: runtimeState?.isReady || false,
    status: runtimeState?.status || 'idle',
    errors: runtimeState?.errors || [],
    logs: runtimeState?.logs || []
  };
}
