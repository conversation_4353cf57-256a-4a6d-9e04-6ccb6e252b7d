/**
 * Example usage of the enhanced Sandpack store
 * 
 * This file demonstrates how to use the new Sandpack store features
 * for managing sandbox files, runtime state, and client interactions
 */

'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  useSandpackIntegration, 
  useSandpackRuntime 
} from './sandpack-store';
import { SandpackFiles } from '@codesandbox/sandpack-react';

interface SandpackStoreExampleProps {
  projectId?: string;
}

export function SandpackStoreExample({ projectId }: SandpackStoreExampleProps) {
  const {
    instances,
    activeInstance,
    isLoading,
    error,
    createInstance,
    setActiveInstance,
    updateFile,
    syncFiles,
    getFiles,
    updateRuntimeState,
    getRuntimeState
  } = useSandpackIntegration(projectId);

  const [newInstanceName, setNewInstanceName] = useState('');
  const [selectedFile, setSelectedFile] = useState('');
  const [fileContent, setFileContent] = useState('');

  // Use the specialized runtime hook for the active instance
  const {
    runtimeState,
    files,
    previewUrl,
    updateFiles,
    updateState,
    setPreviewUrl,
    isReady,
    status,
    errors,
    logs
  } = useSandpackRuntime(activeInstance?.id || null);

  // Create a new Sandpack instance
  const handleCreateInstance = async () => {
    if (!newInstanceName.trim()) return;

    try {
      const instanceId = await createInstance({
        id: '',
        name: newInstanceName,
        template: 'react',
        files: {
          '/App.js': {
            code: `import React from 'react';

export default function App() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>Hello from ${newInstanceName}!</h1>
      <p>This is a React sandbox created with the enhanced Sandpack store.</p>
    </div>
  );
}`
          }
        }
      });
      
      console.log('Created instance:', instanceId);
      setNewInstanceName('');
    } catch (error) {
      console.error('Failed to create instance:', error);
    }
  };

  // Update file content
  const handleUpdateFile = async () => {
    if (!activeInstance || !selectedFile || !fileContent) return;

    try {
      await updateFile(activeInstance.id, selectedFile, fileContent);
      console.log('File updated successfully');
    } catch (error) {
      console.error('Failed to update file:', error);
    }
  };

  // Sync all files
  const handleSyncFiles = async () => {
    if (!activeInstance) return;

    const newFiles: SandpackFiles = {
      ...files,
      '/NewFile.js': {
        code: `// New file created at ${new Date().toISOString()}
console.log('Hello from new file!');`
      }
    };

    try {
      await updateFiles(newFiles);
      console.log('Files synced successfully');
    } catch (error) {
      console.error('Failed to sync files:', error);
    }
  };

  // Update runtime state
  const handleUpdateRuntimeState = () => {
    if (!activeInstance) return;

    updateState({
      status: 'running',
      isReady: true,
      logs: [
        ...logs,
        {
          level: 'info',
          message: 'Runtime state updated manually',
          timestamp: new Date()
        }
      ]
    });
  };

  // Load file content when selection changes
  useEffect(() => {
    if (selectedFile && files[selectedFile]) {
      const file = files[selectedFile];
      setFileContent(typeof file === 'string' ? file : file.code || '');
    }
  }, [selectedFile, files]);

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Sandpack Store Example</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Instance Creation */}
          <div className="flex gap-2">
            <Input
              placeholder="Instance name"
              value={newInstanceName}
              onChange={(e) => setNewInstanceName(e.target.value)}
            />
            <Button onClick={handleCreateInstance} disabled={isLoading}>
              Create Instance
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <p className="text-red-700">Error: {error.message}</p>
            </div>
          )}

          {/* Instance List */}
          <div>
            <h3 className="font-medium mb-2">Instances ({instances.length})</h3>
            <div className="flex flex-wrap gap-2">
              {instances.map((instance) => (
                <Button
                  key={instance.id}
                  variant={activeInstance?.id === instance.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveInstance(instance.id)}
                >
                  {instance.name}
                  <Badge variant="secondary" className="ml-2">
                    {instance.status}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Active Instance Details */}
          {activeInstance && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Active Instance: {activeInstance.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Runtime State */}
                <div>
                  <h4 className="font-medium mb-2">Runtime State</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>Status: <Badge>{status}</Badge></div>
                    <div>Ready: <Badge variant={isReady ? "default" : "secondary"}>{isReady ? "Yes" : "No"}</Badge></div>
                    <div>Errors: <Badge variant="destructive">{errors.length}</Badge></div>
                    <div>Logs: <Badge>{logs.length}</Badge></div>
                  </div>
                  <Button size="sm" onClick={handleUpdateRuntimeState} className="mt-2">
                    Update Runtime State
                  </Button>
                </div>

                {/* File Management */}
                <div>
                  <h4 className="font-medium mb-2">Files ({Object.keys(files).length})</h4>
                  <div className="space-y-2">
                    <select
                      value={selectedFile}
                      onChange={(e) => setSelectedFile(e.target.value)}
                      className="w-full p-2 border rounded"
                    >
                      <option value="">Select a file</option>
                      {Object.keys(files).map((filePath) => (
                        <option key={filePath} value={filePath}>
                          {filePath}
                        </option>
                      ))}
                    </select>
                    
                    {selectedFile && (
                      <div className="space-y-2">
                        <Textarea
                          value={fileContent}
                          onChange={(e) => setFileContent(e.target.value)}
                          rows={10}
                          className="font-mono text-sm"
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={handleUpdateFile}>
                            Update File
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleSyncFiles}>
                            Sync All Files
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Preview URL */}
                {previewUrl && (
                  <div>
                    <h4 className="font-medium mb-2">Preview URL</h4>
                    <Input value={previewUrl} readOnly />
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default SandpackStoreExample;
