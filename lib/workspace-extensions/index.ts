/**
 * Workspace Extensions - Main Export
 * 
 * Main entry point for the workspace extension system
 */

// Core types and interfaces
export * from './types';
export * from './api';

// Extension manager
export {
  WorkspaceExtensionManager,
  ExtensionManagerEvent,
  getExtensionManager
} from './extension-manager';

// Context creation
export { createExtensionContext, updateExtensionContextWithInstance } from './context';

// API implementations
export { createWorkspaceAPI } from './workspace-api';
export { createWorkspaceUIAPI, getCurrentDashboardWidgets, getCurrentHeaderActions } from './ui-api';
export { createNodeboxAPI } from './nodebox-api';
export { createAIAssistantAPI, getCurrentAITools } from './ai-api';

// Utility functions
export { validateExtensionManifest, createExtensionId } from './utils';

// React hooks for extension integration
export {
  useExtensions,
  useExtensionContributions,
  useMainTabContributions,
  usePanelTabContributions,
  useDashboardWidgetContributions,
  useAIToolContributions,
  useHeaderActionContributions,
  useExtensionRegistration,
  useExtension
} from './hooks';
export { useExtensionContext, useExtensionManager } from './components/extension-provider';

// React components for extension integration
export * from './components';

// Extension registry for built-in extensions
export { registerBuiltInExtensions } from './built-in-extensions';

/**
 * Initialize the extension manager
 */
export async function initializeExtensionManager(projectId: string) {
  try {
    const { WorkspaceExtensionManager } = await import('./extension-manager');
    const manager = new WorkspaceExtensionManager();
    await manager.initialize(projectId);
    return manager;
  } catch (error) {
    console.error('Failed to initialize extension manager:', error);
    throw new Error(`Extension manager initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Initialize the workspace extension system
 */
export async function initializeWorkspaceExtensions(projectId: string) {
  try {
    const extensionManager = await initializeExtensionManager(projectId);

    // Register built-in extensions
    try {
      await registerBuiltInExtensions(extensionManager, projectId);
    } catch (error) {
      console.warn('Failed to register some built-in extensions:', error);
      // Continue even if built-in extensions fail
    }

    return extensionManager;
  } catch (error) {
    console.error('Failed to initialize workspace extensions:', error);
    throw new Error(`Workspace extension system initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
