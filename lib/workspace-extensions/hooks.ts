/**
 * Extension System React Hooks
 * 
 * React hooks for integrating with the workspace extension system
 */

import { useState, useEffect, useMemo } from 'react';
import { 
  WorkspaceExtension, 
  ExtensionContributions,
  MainTabContribution,
  PanelTabContribution,
  DashboardWidgetContribution,
  AIToolContribution,
  HeaderActionContribution
} from './types';
import { getExtensionManager, ExtensionManagerEvent } from './extension-manager';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('extension-hooks');

/**
 * Hook to get all extensions and their state
 */
export function useExtensions() {
  const [extensions, setExtensions] = useState<WorkspaceExtension[]>([]);
  const [activeExtensions, setActiveExtensions] = useState<WorkspaceExtension[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const extensionManager = getExtensionManager();

    // Check if extension manager is available
    if (!extensionManager) {
      setLoading(false);
      return;
    }

    // Initial load
    try {
      setExtensions(extensionManager.getAllExtensions());
      setActiveExtensions(extensionManager.getActiveExtensions());
      setLoading(false);
    } catch (err) {
      setError(err as Error);
      setLoading(false);
    }

    // Listen for extension events
    const handleExtensionRegistered = () => {
      setExtensions(extensionManager.getAllExtensions());
    };

    const handleExtensionActivated = () => {
      setActiveExtensions(extensionManager.getActiveExtensions());
    };

    const handleExtensionDeactivated = () => {
      setActiveExtensions(extensionManager.getActiveExtensions());
    };

    const handleExtensionError = ({ error }: { error: Error }) => {
      setError(error);
    };

    extensionManager.on(ExtensionManagerEvent.EXTENSION_REGISTERED, handleExtensionRegistered);
    extensionManager.on(ExtensionManagerEvent.EXTENSION_ACTIVATED, handleExtensionActivated);
    extensionManager.on(ExtensionManagerEvent.EXTENSION_DEACTIVATED, handleExtensionDeactivated);
    extensionManager.on(ExtensionManagerEvent.EXTENSION_ERROR, handleExtensionError);

    return () => {
      extensionManager.off(ExtensionManagerEvent.EXTENSION_REGISTERED, handleExtensionRegistered);
      extensionManager.off(ExtensionManagerEvent.EXTENSION_ACTIVATED, handleExtensionActivated);
      extensionManager.off(ExtensionManagerEvent.EXTENSION_DEACTIVATED, handleExtensionDeactivated);
      extensionManager.off(ExtensionManagerEvent.EXTENSION_ERROR, handleExtensionError);
    };
  }, []);

  const activateExtension = async (extensionId: string) => {
    try {
      const extensionManager = getExtensionManager();
      if (!extensionManager) {
        throw new Error('Extension manager not available');
      }
      await extensionManager.activateExtension(extensionId);
      setError(null);
    } catch (err) {
      setError(err as Error);
      logger.error(`Failed to activate extension ${extensionId}:`, err);
    }
  };

  const deactivateExtension = async (extensionId: string) => {
    try {
      const extensionManager = getExtensionManager();
      if (!extensionManager) {
        throw new Error('Extension manager not available');
      }
      await extensionManager.deactivateExtension(extensionId);
      setError(null);
    } catch (err) {
      setError(err as Error);
      logger.error(`Failed to deactivate extension ${extensionId}:`, err);
    }
  };

  return {
    extensions,
    activeExtensions,
    loading,
    error,
    activateExtension,
    deactivateExtension
  };
}

/**
 * Hook to get extension contributions
 */
export function useExtensionContributions() {
  const [contributions, setContributions] = useState<ExtensionContributions>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const extensionManager = getExtensionManager();

    // Check if extension manager is available
    if (!extensionManager) {
      setLoading(false);
      return;
    }

    // Initial load
    try {
      setContributions(extensionManager.getContributions());
      setLoading(false);
    } catch (err) {
      logger.error('Failed to load extension contributions:', err);
      setLoading(false);
    }

    // Listen for contribution changes
    const handleContributionsChanged = (newContributions: ExtensionContributions) => {
      setContributions(newContributions);
    };

    extensionManager.on(ExtensionManagerEvent.CONTRIBUTIONS_CHANGED, handleContributionsChanged);

    return () => {
      extensionManager.off(ExtensionManagerEvent.CONTRIBUTIONS_CHANGED, handleContributionsChanged);
    };
  }, []);

  // Memoized getters for specific contribution types
  const mainTabs = useMemo(() => 
    (contributions.mainTabs || []).sort((a, b) => (a.order || 0) - (b.order || 0)),
    [contributions.mainTabs]
  );

  const leftPanelTabs = useMemo(() => 
    (contributions.leftPanelTabs || []).sort((a, b) => (a.order || 0) - (b.order || 0)),
    [contributions.leftPanelTabs]
  );

  const rightPanelTabs = useMemo(() => 
    (contributions.rightPanelTabs || []).sort((a, b) => (a.order || 0) - (b.order || 0)),
    [contributions.rightPanelTabs]
  );

  const bottomPanelTabs = useMemo(() => 
    (contributions.bottomPanelTabs || []).sort((a, b) => (a.order || 0) - (b.order || 0)),
    [contributions.bottomPanelTabs]
  );

  const dashboardWidgets = useMemo(() => 
    (contributions.dashboardWidgets || []).sort((a, b) => (a.order || 0) - (b.order || 0)),
    [contributions.dashboardWidgets]
  );

  const aiTools = useMemo(() => 
    contributions.aiTools || [],
    [contributions.aiTools]
  );

  const headerActions = useMemo(() => 
    (contributions.headerActions || []).sort((a, b) => (a.order || 0) - (b.order || 0)),
    [contributions.headerActions]
  );

  return {
    contributions,
    loading,
    mainTabs,
    leftPanelTabs,
    rightPanelTabs,
    bottomPanelTabs,
    dashboardWidgets,
    aiTools,
    headerActions
  };
}

/**
 * Hook to get main tab contributions
 */
export function useMainTabContributions(): MainTabContribution[] {
  const { mainTabs } = useExtensionContributions();
  return mainTabs;
}

/**
 * Hook to get panel tab contributions
 */
export function usePanelTabContributions(panelId: 'left' | 'right' | 'bottom'): PanelTabContribution[] {
  const { leftPanelTabs, rightPanelTabs, bottomPanelTabs } = useExtensionContributions();
  
  switch (panelId) {
    case 'left': return leftPanelTabs;
    case 'right': return rightPanelTabs;
    case 'bottom': return bottomPanelTabs;
  }
}

/**
 * Hook to get dashboard widget contributions
 */
export function useDashboardWidgetContributions(): DashboardWidgetContribution[] {
  const { dashboardWidgets } = useExtensionContributions();
  return dashboardWidgets;
}

/**
 * Hook to get AI tool contributions
 */
export function useAIToolContributions(): AIToolContribution[] {
  const { aiTools } = useExtensionContributions();
  return aiTools;
}

/**
 * Hook to get header action contributions
 */
export function useHeaderActionContributions(): HeaderActionContribution[] {
  const { headerActions } = useExtensionContributions();
  return headerActions;
}

/**
 * Hook to register an extension dynamically
 */
export function useExtensionRegistration() {
  const [registering, setRegistering] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const registerExtension = async (extension: WorkspaceExtension, projectId: string) => {
    setRegistering(true);
    setError(null);

    try {
      const extensionManager = getExtensionManager();
      await extensionManager.registerExtension(extension, projectId);
      logger.info(`Successfully registered extension: ${extension.id}`);
    } catch (err) {
      setError(err as Error);
      logger.error(`Failed to register extension ${extension.id}:`, err);
    } finally {
      setRegistering(false);
    }
  };

  const unregisterExtension = async (extensionId: string) => {
    setRegistering(true);
    setError(null);

    try {
      const extensionManager = getExtensionManager();
      await extensionManager.unregisterExtension(extensionId);
      logger.info(`Successfully unregistered extension: ${extensionId}`);
    } catch (err) {
      setError(err as Error);
      logger.error(`Failed to unregister extension ${extensionId}:`, err);
    } finally {
      setRegistering(false);
    }
  };

  return {
    registerExtension,
    unregisterExtension,
    registering,
    error
  };
}

/**
 * Hook to get a specific extension
 */
export function useExtension(extensionId: string) {
  const [extension, setExtension] = useState<WorkspaceExtension | null>(null);
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    const extensionManager = getExtensionManager();
    
    const updateExtension = () => {
      const ext = extensionManager.getExtension(extensionId);
      setExtension(ext || null);
      
      const activeExtensions = extensionManager.getActiveExtensions();
      setIsActive(activeExtensions.some(e => e.id === extensionId));
    };

    // Initial load
    updateExtension();

    // Listen for changes
    const handleExtensionEvent = () => updateExtension();

    extensionManager.on(ExtensionManagerEvent.EXTENSION_REGISTERED, handleExtensionEvent);
    extensionManager.on(ExtensionManagerEvent.EXTENSION_ACTIVATED, handleExtensionEvent);
    extensionManager.on(ExtensionManagerEvent.EXTENSION_DEACTIVATED, handleExtensionEvent);

    return () => {
      extensionManager.off(ExtensionManagerEvent.EXTENSION_REGISTERED, handleExtensionEvent);
      extensionManager.off(ExtensionManagerEvent.EXTENSION_ACTIVATED, handleExtensionEvent);
      extensionManager.off(ExtensionManagerEvent.EXTENSION_DEACTIVATED, handleExtensionEvent);
    };
  }, [extensionId]);

  return {
    extension,
    isActive
  };
}
