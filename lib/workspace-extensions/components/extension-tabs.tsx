/**
 * Extension Tabs Component
 * 
 * React component that renders extension-contributed tabs
 */

"use client";

import React from 'react';
import { TabData } from '@/components/ui/tabs-layout/types';
import {
  useMainTabContributions,
  usePanelTabContributions,
  useExtensionContext
} from '../index';
import { MainTabContribution, PanelTabContribution } from '../types';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('extension-tabs');

interface ExtensionTabsProps {
  projectId: string;
  type: 'main' | 'panel';
  panelId?: 'left' | 'right' | 'bottom';
}

/**
 * Convert extension contributions to TabData format
 */
function convertMainTabContributionsToTabData(
  contributions: MainTabContribution[],
  projectId: string
): TabData[] {
  return contributions.map(contribution => ({
    id: contribution.id,
    title: contribution.title,
    icon: contribution.icon,
    content: React.createElement(contribution.component, {
      projectId,
      context: null // Will be provided by the extension system
    }),
    isActive: false,
    isPinned: contribution.pinnable || false,
    isDirty: false
  }));
}

function convertPanelTabContributionsToTabData(
  contributions: PanelTabContribution[],
  projectId: string,
  panelId: 'left' | 'right' | 'bottom'
): TabData[] {
  return contributions.map(contribution => ({
    id: contribution.id,
    title: contribution.title,
    icon: contribution.icon,
    content: React.createElement(contribution.component, {
      projectId,
      panelId,
      context: null // Will be provided by the extension system
    }),
    isActive: false,
    isPinned: contribution.pinnable || false,
    isDirty: false
  }));
}

/**
 * Hook to get extension tabs for main area
 */
export function useExtensionMainTabs(projectId: string): TabData[] {
  const mainTabContributions = useMainTabContributions();
  const { isInitialized } = useExtensionContext();

  if (!isInitialized) {
    return [];
  }

  try {
    return convertMainTabContributionsToTabData(mainTabContributions, projectId);
  } catch (error) {
    logger.error('Failed to convert main tab contributions:', error);
    return [];
  }
}

/**
 * Hook to get extension tabs for panels
 */
export function useExtensionPanelTabs(
  projectId: string, 
  panelId: 'left' | 'right' | 'bottom'
): TabData[] {
  const panelTabContributions = usePanelTabContributions(panelId);
  const { isInitialized } = useExtensionContext();

  if (!isInitialized) {
    return [];
  }

  try {
    return convertPanelTabContributionsToTabData(panelTabContributions, projectId, panelId);
  } catch (error) {
    logger.error(`Failed to convert ${panelId} panel tab contributions:`, error);
    return [];
  }
}

/**
 * Component to render extension main tabs
 */
export function ExtensionMainTabs({ projectId }: { projectId: string }) {
  const extensionTabs = useExtensionMainTabs(projectId);
  const { isInitialized, error } = useExtensionContext();

  if (!isInitialized) {
    return null;
  }

  if (error) {
    logger.error('Extension system error:', error);
    return null;
  }

  if (extensionTabs.length === 0) {
    return null;
  }

  return (
    <>
      {extensionTabs.map(tab => (
        <div key={tab.id} className="hidden">
          {/* Tab content will be rendered by the TabGroup component */}
          {tab.content}
        </div>
      ))}
    </>
  );
}

/**
 * Component to render extension panel tabs
 */
export function ExtensionPanelTabs({ 
  projectId, 
  panelId 
}: { 
  projectId: string; 
  panelId: 'left' | 'right' | 'bottom';
}) {
  const extensionTabs = useExtensionPanelTabs(projectId, panelId);
  const { isInitialized, error } = useExtensionContext();

  if (!isInitialized) {
    return null;
  }

  if (error) {
    logger.error('Extension system error:', error);
    return null;
  }

  if (extensionTabs.length === 0) {
    return null;
  }

  return (
    <>
      {extensionTabs.map(tab => (
        <div key={tab.id} className="hidden">
          {/* Tab content will be rendered by the TabGroup component */}
          {tab.content}
        </div>
      ))}
    </>
  );
}

/**
 * Utility function to merge extension tabs with existing tabs
 */
export function mergeExtensionTabs(
  existingTabs: TabData[],
  extensionTabs: TabData[]
): TabData[] {
  // Combine existing tabs with extension tabs
  const allTabs = [...existingTabs, ...extensionTabs];
  
  // Remove duplicates based on ID
  const uniqueTabs = allTabs.filter((tab, index, array) => 
    array.findIndex(t => t.id === tab.id) === index
  );
  
  return uniqueTabs;
}

/**
 * Hook to get merged tabs (existing + extension)
 */
export function useMergedMainTabs(
  projectId: string,
  existingTabs: TabData[]
): TabData[] {
  const extensionTabs = useExtensionMainTabs(projectId);
  return mergeExtensionTabs(existingTabs, extensionTabs);
}

/**
 * Hook to get merged panel tabs (existing + extension)
 */
export function useMergedPanelTabs(
  projectId: string,
  panelId: 'left' | 'right' | 'bottom',
  existingTabs: TabData[]
): TabData[] {
  const extensionTabs = useExtensionPanelTabs(projectId, panelId);
  return mergeExtensionTabs(existingTabs, extensionTabs);
}
