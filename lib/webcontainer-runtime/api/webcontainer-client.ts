/**
 * WebContainer API Client
 * 
 * HTTP client for communicating with WebContainer API routes
 */

import {
  WebContainerInstance,
  WebContainerConfig,
  WebContainerAPIResponse,
  CreateContainerRequest,
  ExecuteCommandRequest,
  FileOperationRequest,
  ProcessInfo,
  FileEntry,
  WebContainerError
} from '../types';
import { FileSystemTree } from '@webcontainer/api';

export class WebContainerClient {
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(baseUrl: string = '/api/webcontainer-api') {
    this.baseUrl = baseUrl;
    this.headers = {
      'Content-Type': 'application/json'
    };
  }

  /**
   * Set authentication headers
   */
  setAuthHeaders(headers: Record<string, string>): void {
    this.headers = { ...this.headers, ...headers };
  }

  /**
   * Create a new WebContainer
   */
  async createContainer(
    config: WebContainerConfig,
    files?: FileSystemTree
  ): Promise<string> {
    const request: CreateContainerRequest = {
      config,
      files,
      autoMount: !!files
    };

    const response = await this.post<{ containerId: string }>('/', request);
    return response.data.containerId;
  }

  /**
   * Get container information
   */
  async getContainer(containerId: string): Promise<WebContainerInstance> {
    const response = await this.get<WebContainerInstance>(`/${containerId}`);
    return response.data;
  }

  /**
   * List all containers
   */
  async listContainers(): Promise<WebContainerInstance[]> {
    const response = await this.get<WebContainerInstance[]>('/');
    return response.data;
  }

  /**
   * Start a container
   */
  async startContainer(containerId: string): Promise<void> {
    await this.post(`/${containerId}/start`);
  }

  /**
   * Stop a container
   */
  async stopContainer(containerId: string): Promise<void> {
    await this.post(`/${containerId}/stop`);
  }

  /**
   * Destroy a container
   */
  async destroyContainer(containerId: string): Promise<void> {
    await this.delete(`/${containerId}`);
  }

  /**
   * Execute a command in a container
   */
  async executeCommand(
    containerId: string,
    command: string,
    args: string[] = [],
    options: any = {}
  ): Promise<ProcessInfo> {
    const request: ExecuteCommandRequest = {
      command,
      args,
      options
    };

    const response = await this.post<ProcessInfo>(
      `/${containerId}/processes`,
      request
    );
    return response.data;
  }

  /**
   * Get process information
   */
  async getProcess(containerId: string, processId: string): Promise<ProcessInfo> {
    const response = await this.get<ProcessInfo>(
      `/${containerId}/processes/${processId}`
    );
    return response.data;
  }

  /**
   * Kill a process
   */
  async killProcess(
    containerId: string,
    processId: string,
    signal: string = 'SIGTERM'
  ): Promise<void> {
    await this.post(`/${containerId}/processes/${processId}/kill`, { signal });
  }

  /**
   * Get process output
   */
  async getProcessOutput(
    containerId: string,
    processId: string,
    lines?: number
  ): Promise<any[]> {
    const params = lines ? `?lines=${lines}` : '';
    const response = await this.get<any[]>(
      `/${containerId}/processes/${processId}/output${params}`
    );
    return response.data;
  }

  /**
   * Read file from container
   */
  async readFile(containerId: string, path: string): Promise<string> {
    const response = await this.get<{ content: string }>(
      `/${containerId}/filesystem/read?path=${encodeURIComponent(path)}`
    );
    return response.data.content;
  }

  /**
   * Write file to container
   */
  async writeFile(
    containerId: string,
    path: string,
    content: string
  ): Promise<void> {
    const request: FileOperationRequest = {
      operation: {
        type: 'create',
        path,
        content,
        timestamp: new Date()
      }
    };

    await this.post(`/${containerId}/filesystem/write`, request);
  }

  /**
   * Delete file from container
   */
  async deleteFile(containerId: string, path: string): Promise<void> {
    const request: FileOperationRequest = {
      operation: {
        type: 'delete',
        path,
        timestamp: new Date()
      }
    };

    await this.post(`/${containerId}/filesystem/delete`, request);
  }

  /**
   * List files in container directory
   */
  async listFiles(containerId: string, path: string = '/'): Promise<FileEntry[]> {
    const response = await this.get<FileEntry[]>(
      `/${containerId}/filesystem/list?path=${encodeURIComponent(path)}`
    );
    return response.data;
  }

  /**
   * Mount files to container
   */
  async mountFiles(
    containerId: string,
    files: FileSystemTree,
    merge: boolean = false
  ): Promise<void> {
    await this.post(`/${containerId}/filesystem/mount`, {
      files,
      options: { merge }
    });
  }

  /**
   * Export container filesystem
   */
  async exportFilesystem(containerId: string): Promise<FileSystemTree> {
    const response = await this.get<FileSystemTree>(
      `/${containerId}/filesystem/export`
    );
    return response.data;
  }

  /**
   * Start development server
   */
  async startDevServer(
    containerId: string,
    command: string = 'npm run dev'
  ): Promise<ProcessInfo> {
    const response = await this.post<ProcessInfo>(
      `/${containerId}/dev-server/start`,
      { command }
    );
    return response.data;
  }

  /**
   * Install packages
   */
  async installPackages(
    containerId: string,
    packages: string[],
    options: any = {}
  ): Promise<ProcessInfo> {
    const response = await this.post<ProcessInfo>(
      `/${containerId}/packages/install`,
      { packages, options }
    );
    return response.data;
  }

  /**
   * Run npm script
   */
  async runScript(
    containerId: string,
    scriptName: string,
    packageManager: string = 'npm'
  ): Promise<ProcessInfo> {
    const response = await this.post<ProcessInfo>(
      `/${containerId}/scripts/run`,
      { scriptName, packageManager }
    );
    return response.data;
  }

  // Private HTTP methods
  private async get<T>(
    endpoint: string
  ): Promise<WebContainerAPIResponse<T>> {
    return this.request<T>('GET', endpoint);
  }

  private async post<T>(
    endpoint: string,
    data?: any
  ): Promise<WebContainerAPIResponse<T>> {
    return this.request<T>('POST', endpoint, data);
  }

  private async put<T>(
    endpoint: string,
    data?: any
  ): Promise<WebContainerAPIResponse<T>> {
    return this.request<T>('PUT', endpoint, data);
  }

  private async delete<T>(
    endpoint: string
  ): Promise<WebContainerAPIResponse<T>> {
    return this.request<T>('DELETE', endpoint);
  }

  private async request<T>(
    method: string,
    endpoint: string,
    data?: any
  ): Promise<WebContainerAPIResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      method,
      headers: this.headers
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `HTTP ${response.status}: ${response.statusText}`;
        console.error('WebContainer API Error:', {
          url,
          method,
          status: response.status,
          statusText: response.statusText,
          errorData
        });
        throw this.createError(
          'API_REQUEST_FAILED',
          errorMessage
        );
      }

      const result = await response.json();

      // Check if the API response indicates an error
      if (result && !result.success && result.error) {
        throw this.createError(
          'API_RESPONSE_ERROR',
          result.error
        );
      }

      return result;

    } catch (error) {
      console.error('WebContainer Client Error:', {
        url,
        method,
        error: error instanceof Error ? error.message : error
      });

      if (error instanceof Error && error.message.includes('fetch')) {
        throw this.createError(
          'NETWORK_ERROR',
          `Network error: ${error.message}`
        );
      }
      throw error;
    }
  }

  private createError(code: string, message: string): WebContainerError {
    return {
      code,
      message,
      timestamp: new Date()
    };
  }
}
