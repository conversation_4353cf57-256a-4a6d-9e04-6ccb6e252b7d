/**
 * Client-Side WebContainer Manager
 * 
 * Manages actual WebContainer instances in the browser
 * Coordinates with server-side metadata API
 */

import { WebContainer, FileSystemTree } from '@webcontainer/api';
import { EventEmitter } from 'events';
import {
  WebContainerInstance,
  WebContainerConfig,
  WebContainerError,
  ProcessInfo,
  ProcessOptions,
  FileEntry
} from '../types';

export class ClientWebContainerManager extends EventEmitter {
  private containers: Map<string, WebContainerInstance> = new Map();
  private isInitialized = false;
  private apiBaseUrl = '/api/webcontainer-api';

  constructor() {
    super();
    this.setMaxListeners(100);
  }

  /**
   * Initialize the client-side WebContainer manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        throw new Error('ClientWebContainerManager requires a browser environment');
      }

      // Check if WebContainer API is available
      if (typeof WebContainer === 'undefined') {
        throw new Error('WebContainer API is not available. Make sure @webcontainer/api is properly loaded.');
      }

      console.log('Client WebContainer manager initialized successfully');
      this.isInitialized = true;
      this.emit('manager:initialized');
    } catch (error) {
      console.error('Client WebContainer manager initialization failed:', error);
      throw error;
    }
  }

  /**
   * Create a new WebContainer instance
   */
  async createContainer(
    config: WebContainerConfig,
    files?: FileSystemTree
  ): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // First, create metadata on server
      const response = await fetch(this.apiBaseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config,
          files,
          autoMount: true
        }),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to create container metadata');
      }

      const containerId = result.data.containerId;

      // Now create actual WebContainer instance
      console.log('Booting WebContainer...');
      const container = await WebContainer.boot();
      console.log('WebContainer booted successfully');

      // Mount files if provided
      if (files) {
        console.log('Mounting files to WebContainer...');
        await container.mount(files);
        console.log('Files mounted successfully');
      }

      const instance: WebContainerInstance = {
        id: containerId,
        name: config.name,
        container,
        config,
        status: 'ready',
        createdAt: new Date(),
        lastActivity: new Date(),
        metadata: {}
      };

      this.containers.set(containerId, instance);
      this.setupContainerEventListeners(instance);
      this.emit('container:created', containerId, { config });

      console.log(`WebContainer created successfully: ${containerId}`);
      return containerId;
    } catch (error) {
      console.error('WebContainer creation failed:', error);
      throw error;
    }
  }

  /**
   * Get container instance by ID
   */
  getContainer(containerId: string): WebContainerInstance | null {
    return this.containers.get(containerId) || null;
  }

  /**
   * List all local containers
   */
  listContainers(): WebContainerInstance[] {
    return Array.from(this.containers.values());
  }

  /**
   * List all containers (including server metadata)
   */
  async listAllContainers(projectId?: string): Promise<any[]> {
    try {
      const url = new URL(this.apiBaseUrl, window.location.origin);
      if (projectId) {
        url.searchParams.set('projectId', projectId);
      }

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch containers');
      }

      return result.data;
    } catch (error) {
      console.error('Failed to fetch containers from server:', error);
      return this.listContainers();
    }
  }

  /**
   * Execute a command in a container
   */
  async executeCommand(
    containerId: string,
    command: string,
    args: string[] = [],
    options: ProcessOptions = {}
  ): Promise<ProcessInfo> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw new Error(`Container ${containerId} not found`);
    }

    const processId = this.generateProcessId();

    try {
      const processInfo: ProcessInfo = {
        id: processId,
        containerId,
        command,
        args,
        cwd: options.cwd || '/',
        env: options.env || {},
        status: 'starting',
        startTime: new Date()
      };

      // Spawn process in WebContainer
      const process = await instance.container.spawn(command, args, {
        cwd: options.cwd,
        env: options.env
      });

      processInfo.status = 'running';
      processInfo.pid = process.pid;

      // Set up output streaming
      process.output.pipeTo(new WritableStream({
        write: (data) => {
          this.emit('process:output', {
            processId,
            containerId,
            type: 'stdout',
            data: data.toString(),
            timestamp: new Date()
          });
        }
      }));

      // Handle process completion
      process.exit.then((exitCode) => {
        processInfo.status = exitCode === 0 ? 'completed' : 'failed';
        processInfo.exitCode = exitCode;
        processInfo.endTime = new Date();
        
        this.emit('process:completed', { processInfo });
      }).catch((error) => {
        processInfo.status = 'failed';
        processInfo.endTime = new Date();
        
        this.emit('process:failed', { processInfo, error });
      });

      this.emit('process:started', { processInfo });
      instance.lastActivity = new Date();

      return processInfo;
    } catch (error) {
      throw new Error(`Failed to execute command: ${error}`);
    }
  }

  /**
   * Read file from container
   */
  async readFile(containerId: string, path: string): Promise<string> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      const file = await instance.container.fs.readFile(path, 'utf-8');
      instance.lastActivity = new Date();
      return file;
    } catch (error) {
      throw new Error(`Failed to read file ${path}: ${error}`);
    }
  }

  /**
   * Write file to container
   */
  async writeFile(containerId: string, path: string, content: string): Promise<void> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      await instance.container.fs.writeFile(path, content);
      instance.lastActivity = new Date();
      this.emit('filesystem:changed', { containerId, path, type: 'write' });
    } catch (error) {
      throw new Error(`Failed to write file ${path}: ${error}`);
    }
  }

  /**
   * List files in container directory
   */
  async listFiles(containerId: string, path: string = '/'): Promise<FileEntry[]> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      const entries = await instance.container.fs.readdir(path, { withFileTypes: true });
      instance.lastActivity = new Date();
      
      return entries.map(entry => ({
        name: entry.name,
        path: `${path}/${entry.name}`.replace('//', '/'),
        type: entry.isDirectory() ? 'directory' : 'file',
        size: 0, // WebContainer doesn't provide size info
        lastModified: new Date()
      }));
    } catch (error) {
      throw new Error(`Failed to list files in ${path}: ${error}`);
    }
  }

  /**
   * Destroy a container
   */
  async destroyContainer(containerId: string): Promise<void> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      // Tear down the WebContainer
      await instance.container.teardown();
      
      instance.status = 'destroyed';
      this.containers.delete(containerId);
      
      this.emit('container:destroyed', { containerId });
    } catch (error) {
      throw new Error(`Failed to destroy container: ${error}`);
    }
  }

  /**
   * Cleanup all containers
   */
  async cleanup(): Promise<void> {
    const containerIds = Array.from(this.containers.keys());
    
    for (const containerId of containerIds) {
      try {
        await this.destroyContainer(containerId);
      } catch (error) {
        console.error(`Failed to cleanup container ${containerId}:`, error);
      }
    }
    
    this.containers.clear();
  }

  // Private helper methods
  private generateProcessId(): string {
    return `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupContainerEventListeners(instance: WebContainerInstance): void {
    // Set up any container-specific event listeners here
  }
}

// Export singleton instance
export const clientWebContainerManager = new ClientWebContainerManager();
