/**
 * WebContainer Manager
 * 
 * Core manager for WebContainer lifecycle and operations
 */

import { WebContainer, FileSystemTree } from '@webcontainer/api';
import { EventEmitter } from 'events';
import {
  WebContainerInstance,
  WebContainerConfig,
  WebContainerStatus,
  WebContainerError,
  WebContainerEvent,
  WebContainerEventType,
  ProcessInfo,
  ProcessOptions,
  FileEntry
} from '../types';

export class WebContainerManager extends EventEmitter {
  private containers: Map<string, WebContainerInstance> = new Map();
  private isInitialized = false;

  constructor() {
    super();
    this.setMaxListeners(100); // Allow many listeners for events
  }

  /**
   * Initialize the WebContainer manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        throw new Error('WebContainer API requires a browser environment');
      }

      // Check if WebContainer API is available
      if (typeof WebContainer === 'undefined') {
        throw new Error('WebContainer API is not available. Make sure @webcontainer/api is properly loaded.');
      }

      console.log('WebContainer manager initialized successfully');
      this.isInitialized = true;
      this.emit('manager:initialized');
    } catch (error) {
      console.error('WebContainer manager initialization failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      const wcError: WebContainerError = {
        code: 'MANAGER_INITIALIZATION_FAILED',
        message: `Failed to initialize WebContainer manager: ${errorMessage}`,
        timestamp: new Date()
      };
      this.emit('manager:error', wcError);
      throw wcError;
    }
  }

  /**
   * Create a new WebContainer instance
   */
  async createContainer(
    config: WebContainerConfig,
    files?: FileSystemTree
  ): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const containerId = this.generateContainerId();

    try {
      // Check if WebContainer is available (browser environment)
      if (typeof window === 'undefined') {
        throw new Error('WebContainer API is only available in browser environments');
      }

      // Boot WebContainer
      console.log('Booting WebContainer...');
      const container = await WebContainer.boot();
      console.log('WebContainer booted successfully');

      // Mount files if provided
      if (files) {
        console.log('Mounting files to WebContainer...');
        await container.mount(files);
        console.log('Files mounted successfully');
      }

      const instance: WebContainerInstance = {
        id: containerId,
        name: config.name,
        container,
        config,
        status: 'ready',
        createdAt: new Date(),
        lastActivity: new Date(),
        metadata: {}
      };

      this.containers.set(containerId, instance);

      // Set up event listeners
      this.setupContainerEventListeners(instance);

      this.emitEvent('container:created', containerId, { config });
      console.log(`WebContainer created successfully: ${containerId}`);

      return containerId;
    } catch (error) {
      console.error('WebContainer creation failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      const wcError: WebContainerError = {
        code: 'CONTAINER_CREATION_FAILED',
        message: `Failed to create container: ${errorMessage}`,
        containerId,
        timestamp: new Date()
      };
      this.emitEvent('container:error', containerId, wcError);
      throw wcError;
    }
  }

  /**
   * Get container instance by ID
   */
  getContainer(containerId: string): WebContainerInstance | null {
    return this.containers.get(containerId) || null;
  }

  /**
   * List all containers
   */
  listContainers(): WebContainerInstance[] {
    return Array.from(this.containers.values());
  }

  /**
   * Start a container (if it has a start script)
   */
  async startContainer(containerId: string): Promise<void> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw this.createError('CONTAINER_NOT_FOUND', `Container ${containerId} not found`);
    }

    try {
      instance.status = 'running';
      instance.lastActivity = new Date();
      this.emitEvent('container:started', containerId, {});
    } catch (error) {
      instance.status = 'error';
      const wcError = this.createError(
        'CONTAINER_START_FAILED',
        `Failed to start container: ${error}`,
        containerId
      );
      this.emitEvent('container:error', containerId, wcError);
      throw wcError;
    }
  }

  /**
   * Stop a container
   */
  async stopContainer(containerId: string): Promise<void> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw this.createError('CONTAINER_NOT_FOUND', `Container ${containerId} not found`);
    }

    try {
      instance.status = 'stopped';
      instance.lastActivity = new Date();
      this.emitEvent('container:stopped', containerId, {});
    } catch (error) {
      const wcError = this.createError(
        'CONTAINER_STOP_FAILED',
        `Failed to stop container: ${error}`,
        containerId
      );
      this.emitEvent('container:error', containerId, wcError);
      throw wcError;
    }
  }

  /**
   * Destroy a container
   */
  async destroyContainer(containerId: string): Promise<void> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw this.createError('CONTAINER_NOT_FOUND', `Container ${containerId} not found`);
    }

    try {
      // Tear down the WebContainer
      await instance.container.teardown();
      
      instance.status = 'destroyed';
      this.containers.delete(containerId);
      
      this.emitEvent('container:destroyed', containerId, {});
    } catch (error) {
      const wcError = this.createError(
        'CONTAINER_DESTROY_FAILED',
        `Failed to destroy container: ${error}`,
        containerId
      );
      this.emitEvent('container:error', containerId, wcError);
      throw wcError;
    }
  }

  /**
   * Execute a command in a container
   */
  async executeCommand(
    containerId: string,
    command: string,
    args: string[] = [],
    options: ProcessOptions = {}
  ): Promise<ProcessInfo> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw this.createError('CONTAINER_NOT_FOUND', `Container ${containerId} not found`);
    }

    const processId = this.generateProcessId();

    try {
      const processInfo: ProcessInfo = {
        id: processId,
        containerId,
        command,
        args,
        cwd: options.cwd || '/',
        env: options.env || {},
        status: 'starting',
        startTime: new Date()
      };

      // Spawn process in WebContainer
      const process = await instance.container.spawn(command, args, {
        cwd: options.cwd,
        env: options.env
      });

      processInfo.status = 'running';
      processInfo.pid = process.pid;

      // Set up output streaming
      process.output.pipeTo(new WritableStream({
        write: (data) => {
          this.emitEvent('process:output', containerId, {
            processId,
            type: 'stdout',
            data: data.toString(),
            timestamp: new Date()
          });
        }
      }));

      // Handle process completion
      process.exit.then((exitCode) => {
        processInfo.status = exitCode === 0 ? 'completed' : 'failed';
        processInfo.exitCode = exitCode;
        processInfo.endTime = new Date();
        
        this.emitEvent('process:completed', containerId, { processInfo });
      }).catch((error) => {
        processInfo.status = 'failed';
        processInfo.endTime = new Date();
        
        this.emitEvent('process:failed', containerId, { processInfo, error });
      });

      this.emitEvent('process:started', containerId, { processInfo });
      instance.lastActivity = new Date();

      return processInfo;
    } catch (error) {
      const wcError = this.createError(
        'PROCESS_EXECUTION_FAILED',
        `Failed to execute command: ${error}`,
        containerId
      );
      throw wcError;
    }
  }

  /**
   * Read file from container
   */
  async readFile(containerId: string, path: string): Promise<string> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw this.createError('CONTAINER_NOT_FOUND', `Container ${containerId} not found`);
    }

    try {
      const file = await instance.container.fs.readFile(path, 'utf-8');
      instance.lastActivity = new Date();
      return file;
    } catch (error) {
      throw this.createError(
        'FILESYSTEM_ERROR',
        `Failed to read file ${path}: ${error}`,
        containerId
      );
    }
  }

  /**
   * Write file to container
   */
  async writeFile(containerId: string, path: string, content: string): Promise<void> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw this.createError('CONTAINER_NOT_FOUND', `Container ${containerId} not found`);
    }

    try {
      await instance.container.fs.writeFile(path, content);
      instance.lastActivity = new Date();
      this.emitEvent('filesystem:changed', containerId, { path, type: 'write' });
    } catch (error) {
      throw this.createError(
        'FILESYSTEM_ERROR',
        `Failed to write file ${path}: ${error}`,
        containerId
      );
    }
  }

  /**
   * List files in container directory
   */
  async listFiles(containerId: string, path: string = '/'): Promise<FileEntry[]> {
    const instance = this.getContainer(containerId);
    if (!instance) {
      throw this.createError('CONTAINER_NOT_FOUND', `Container ${containerId} not found`);
    }

    try {
      const entries = await instance.container.fs.readdir(path, { withFileTypes: true });
      instance.lastActivity = new Date();
      
      return entries.map(entry => ({
        path: `${path}/${entry.name}`.replace('//', '/'),
        type: entry.isDirectory() ? 'directory' : 'file',
        lastModified: new Date()
      }));
    } catch (error) {
      throw this.createError(
        'FILESYSTEM_ERROR',
        `Failed to list files in ${path}: ${error}`,
        containerId
      );
    }
  }

  /**
   * Cleanup all containers
   */
  async cleanup(): Promise<void> {
    const containerIds = Array.from(this.containers.keys());
    
    for (const containerId of containerIds) {
      try {
        await this.destroyContainer(containerId);
      } catch (error) {
        console.error(`Failed to cleanup container ${containerId}:`, error);
      }
    }
    
    this.containers.clear();
    this.isInitialized = false;
  }

  // Private helper methods
  private generateContainerId(): string {
    return `wc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateProcessId(): string {
    return `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private createError(
    code: string,
    message: string,
    containerId?: string,
    processId?: string
  ): WebContainerError {
    return {
      code,
      message,
      containerId,
      processId,
      timestamp: new Date()
    };
  }

  private emitEvent(
    type: WebContainerEventType,
    containerId: string,
    data: any
  ): void {
    const event: WebContainerEvent = {
      type,
      containerId,
      timestamp: new Date(),
      data
    };
    this.emit('event', event);
    this.emit(type, event);
  }

  private setupContainerEventListeners(instance: WebContainerInstance): void {
    // Set up any container-specific event listeners here
    // This can be extended based on WebContainer API capabilities
  }
}
