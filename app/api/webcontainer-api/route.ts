/**
 * WebContainer API Routes
 * 
 * Main API endpoints for WebContainer management
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  WebContainerConfig,
  CreateContainerRequest,
  WebContainerAPIResponse,
  WebContainerError,
  WebContainerInstance
} from '@/lib/webcontainer-runtime/types';

// Server-side container metadata storage
// In production, this would be stored in a database
interface ContainerMetadata {
  id: string;
  name: string;
  config: WebContainerConfig;
  status: 'pending' | 'ready' | 'running' | 'stopped' | 'error' | 'destroyed';
  createdAt: Date;
  lastActivity: Date;
  projectId?: string;
}

// In-memory storage for container metadata
let containerMetadata: Map<string, ContainerMetadata> = new Map();

// Initialize server-side managers (metadata only)
async function initializeManagers() {
  // Server-side initialization is just metadata management
  // Actual WebContainer instances are created client-side
  console.log('WebContainer API server initialized (metadata management only)');
}

/**
 * GET /api/webcontainer-api
 * List all WebContainer instances (metadata)
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();

    // Get query parameters for filtering
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');

    // Convert metadata to container list format
    let containers = Array.from(containerMetadata.values());

    // Filter by project ID if specified
    if (projectId) {
      containers = containers.filter(c => c.projectId === projectId);
    }

    const response: WebContainerAPIResponse = {
      success: true,
      data: containers,
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error listing containers:', error);

    const errorResponse: WebContainerAPIResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date()
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * POST /api/webcontainer-api
 * Create a new WebContainer instance (metadata)
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();

    const body: CreateContainerRequest = await request.json();

    if (!body.config) {
      throw new Error('Container configuration is required');
    }

    // Validate configuration
    if (!body.config.name) {
      throw new Error('Container name is required');
    }

    // Generate container ID
    const containerId = generateContainerId();

    // Create container metadata
    const metadata: ContainerMetadata = {
      id: containerId,
      name: body.config.name,
      config: body.config,
      status: 'pending', // Client will update this when WebContainer is actually created
      createdAt: new Date(),
      lastActivity: new Date(),
      projectId: body.config.projectId
    };

    // Store metadata
    containerMetadata.set(containerId, metadata);

    console.log(`Container metadata created: ${containerId} (${body.config.name})`);

    const response: WebContainerAPIResponse = {
      success: true,
      data: {
        containerId,
        metadata,
        message: 'Container metadata created. Client should initialize WebContainer instance.'
      },
      timestamp: new Date()
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error creating container metadata:', error);

    const errorResponse: WebContainerAPIResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date()
    };

    return NextResponse.json(errorResponse, { status: 400 });
  }
}

/**
 * DELETE /api/webcontainer-api
 * Cleanup all containers (metadata)
 */
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();

    const containerCount = containerMetadata.size;

    // Clear all container metadata
    containerMetadata.clear();

    console.log(`Cleared metadata for ${containerCount} containers`);

    const response: WebContainerAPIResponse = {
      success: true,
      data: {
        message: `Cleared metadata for ${containerCount} containers`,
        note: 'Client-side WebContainer instances should be cleaned up separately'
      },
      timestamp: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error cleaning up container metadata:', error);

    const errorResponse: WebContainerAPIResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date()
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}

// Helper function to generate container IDs
function generateContainerId(): string {
  return `wc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Error handling utility
function createErrorResponse(
  error: unknown,
  status: number = 500
): NextResponse {
  let errorMessage = 'Unknown error';
  let errorCode = 'UNKNOWN_ERROR';

  if (error instanceof Error) {
    errorMessage = error.message;
  }

  if (typeof error === 'object' && error !== null && 'code' in error) {
    errorCode = (error as WebContainerError).code;
  }

  const errorResponse: WebContainerAPIResponse = {
    success: false,
    error: errorMessage,
    timestamp: new Date()
  };

  return NextResponse.json(errorResponse, { status });
}

// Validation utilities
function validateContainerConfig(config: WebContainerConfig): void {
  if (!config.name || typeof config.name !== 'string') {
    throw new Error('Container name is required and must be a string');
  }

  if (config.name.length < 1 || config.name.length > 100) {
    throw new Error('Container name must be between 1 and 100 characters');
  }

  if (config.resources) {
    if (config.resources.memory && config.resources.memory < 128) {
      throw new Error('Memory allocation must be at least 128MB');
    }

    if (config.resources.cpu && config.resources.cpu < 0.1) {
      throw new Error('CPU allocation must be at least 0.1');
    }
  }
}

// Health check endpoint
export async function HEAD(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    console.error('WebContainer API health check failed:', error);
    return new NextResponse(null, { status: 503 });
  }
}

// Enhanced health check with detailed status
export async function OPTIONS(request: NextRequest): Promise<NextResponse> {
  try {
    await initializeManagers();

    const healthStatus = {
      timestamp: new Date().toISOString(),
      environment: 'server',
      apiType: 'metadata-management',
      containersCount: containerMetadata.size,
      containerIds: Array.from(containerMetadata.keys()),
      note: 'This API manages WebContainer metadata. Actual WebContainer instances run client-side.'
    };

    return NextResponse.json({
      success: true,
      health: healthStatus
    });
  } catch (error) {
    console.error('WebContainer API detailed health check failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Health check failed',
      environment: 'server'
    }, { status: 503 });
  }
}
