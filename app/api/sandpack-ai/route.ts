/**
 * Sandpack AI API Route
 * 
 * API endpoint for AI-powered Sandpack assistance
 */

import { NextRequest, NextResponse } from 'next/server';
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';

export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const { messages, projectId } = await request.json();

    const result = await streamText({
      model: openai('gpt-4-turbo-preview'),
      messages: [
        {
          role: 'system',
          content: `You are a specialized Sandpack development assistant. You help developers create, manage, and optimize browser-based development environments using Sandpack.

Key Capabilities:
🏖️ Sandpack Management: Create instances, manage templates, configure environments
📝 Code Generation: Generate code for various frameworks (React, Vue, Angular, etc.)
🔧 Development Tools: Help with debugging, optimization, and best practices
🎨 UI/UX: Assist with component design and styling
📦 Package Management: Help with dependencies and project setup
🚀 Deployment: Guide on deployment strategies and optimization

Current Context:
- Project ID: ${projectId || 'Not specified'}
- Environment: Browser-based Sandpack development
- Focus: Interactive development with live preview

Guidelines:
- Provide practical, actionable advice
- Include code examples when helpful
- Consider browser limitations and Sandpack constraints
- Suggest appropriate templates and frameworks
- Help with debugging and optimization
- Focus on modern web development practices

Always be helpful, concise, and provide working code examples when possible.`
        },
        ...messages
      ],
      temperature: 0.7,
      maxTokens: 2000,
    });

    return result.toAIStreamResponse();
  } catch (error) {
    console.error('Sandpack AI API error:', error);
    return NextResponse.json(
      { error: 'Failed to process Sandpack AI request' },
      { status: 500 }
    );
  }
}
